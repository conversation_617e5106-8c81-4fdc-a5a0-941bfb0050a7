import { Module } from '@nestjs/common';
import { JobsService } from './jobs.service';
import { JobsController } from './jobs.controller';
import { DatabaseModule } from 'src/database/database.module';
import { jobProviders } from './job.providers';
import { PlacesModule } from 'src/places/places.module';
import { VehiclesModule } from 'src/vehicles/vehicles.module';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [DatabaseModule, UsersModule, PlacesModule, VehiclesModule],
  providers: [...jobProviders, JobsService],
  controllers: [JobsController],
  exports: [JobsService],
})
export class JobsModule {}
