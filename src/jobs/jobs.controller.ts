import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { JobsService } from './jobs.service';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { getJobDto } from './dto/job.dto';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('jobs')
export class JobsController {
  constructor(private readonly jobsService: JobsService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post()
  async create(@Request() req, @Body() createJobDto: CreateJobDto) {
    const job = await this.jobsService.create(createJobDto, req.user.userId);
    return getJobDto(job);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('file'))
  @Post('import')
  async import(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: { importAll: string },
    @Request() req,
  ) {
    return await this.jobsService.import(file, dto.importAll, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('import')
  async removeImported(@Request() req) {
    return await this.jobsService.removeImported(req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('all')
  findAllPaginated(@Paginate() query: PaginateQuery, @Request() req) {
    return this.jobsService.findAllPaginated(
      query,
      req.query?.delta === 'true',
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('status-counts')
  findAllStatusCounts() {
    return this.jobsService.findAllStatusCounts();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  async findAll(@Request() req) {
    const jobs = await this.jobsService.findAll(
      req.query?.unassigned === 'true',
      req.query?.extended === 'true',
      req.query?.today,
    );
    return jobs.map((job) => getJobDto(job, req.query?.extended === 'true'));
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('helpers')
  async findAllForHelpers(@Request() req) {
    const jobs = await this.jobsService.findAllForHelpers(req.query?.today);
    return jobs.map((job) => getJobDto(job));
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('deltas')
  async findAllDeltas(@Request() req) {
    const deltas = await this.jobsService.findAllDeltas(req.query?.today);
    return deltas;
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('user/:id/active')
  async findActiveJobsByUser(@Param('id') id: string) {
    const jobs = await this.jobsService.findActiveJobsByUser(id);
    return jobs.map((job) => getJobDto(job, true));
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('user/:id')
  async findByUser(@Request() req, @Param('id') id: string) {
    const jobs = await this.jobsService.findByUser(
      id,
      req.query?.place,
      req.query?.ready === 'true',
      req.query?.today,
      req.query?.driver === 'true',
    );
    return jobs.map((job) => getJobDto(job, true));
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('place/:id')
  findByPlace(@Request() req, @Param('id') id: string) {
    return this.jobsService.findByPlace(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('mine/active')
  async findMyActiveJobs(@Request() req) {
    const jobs = await this.jobsService.findActiveJobsByUser(req.user.userId);
    return jobs.map((job) => getJobDto(job, true));
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('mine')
  async findMine(@Request() req) {
    const jobs = await this.jobsService.findByUser(
      req.user.userId,
      req.query?.place,
      req.query?.ready === 'true',
      req.query?.today,
      req.query?.driver === 'true',
    );
    return jobs.map((job) => getJobDto(job, true));
  }

  @UseGuards(JwtAccessAuthGuard)
  @Post('verify')
  async verifyCleaning(@Body() dto: { id: string }, @Request() req) {
    return this.jobsService.verifyCleaning(dto.id, req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('verifiable')
  async findVerifiable(@Request() req) {
    const jobs = await this.jobsService.findVerifiable(req.query?.today);
    return jobs.map((job) => getJobDto(job, true));
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id/timelogs')
  findTimelogs(@Param('id') id: string) {
    return this.jobsService.findTimelogsForJob(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const job = await this.jobsService.findOne(id);
    return getJobDto(job, true);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateJobDto: UpdateJobDto,
    @Request() req,
  ) {
    const job = await this.jobsService.update(
      id,
      updateJobDto,
      req.user.userId,
    );
    return getJobDto(job);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.jobsService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.jobsService.remove(id, req.user.userId);
  }
}
