import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import xlsx from 'node-xlsx';
import { addMinutes, subMinutes } from 'date-fns';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { repositories } from 'src/common/constants';
import {
  In,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Not,
  Repository,
} from 'typeorm';
import { Job, JobStatus } from './entities/job.entity';
import { UsersService } from 'src/users/users.service';
import { PlacesService } from 'src/places/places.service';
import { VehiclesService } from 'src/vehicles/vehicles.service';
import { Booking } from 'src/vehicles/entities/booking.entity';
import { Vehicle } from 'src/vehicles/entities/vehicle.entity';
import { User } from 'src/users/entities/user.entity';
import { Place, PlaceStatus } from 'src/places/entities/place.entity';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import { ModuleRef } from '@nestjs/core';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';
import { TimelogAction } from 'src/timelogs/entities/timelog.entity';
import {
  FilterOperator,
  paginate,
  Paginated,
  PaginateQuery,
} from 'nestjs-paginate';

const FlaggableDeltaMargin = 0.3; // 30% margin for delta flagging

@Injectable()
export class JobsService implements OnModuleInit {
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.job)
    private jobRepository: Repository<Job>,
    private usersService: UsersService,
    private placesService: PlacesService,
    private vehiclesService: VehiclesService,
  ) {}

  onModuleInit() {
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async create(createJobDto: CreateJobDto, authUserId: string): Promise<Job> {
    let user: User;

    if (createJobDto.userId) {
      // Find user
      user = await this.usersService.findOneById(createJobDto.userId);

      if (!user) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }

      createJobDto.user = user;
      delete createJobDto.userId;

      // Check if the user is available for the job
      const existingJobs = await this.jobRepository.find({
        where: {
          user,
          start: MoreThanOrEqual(new Date(createJobDto.start)),
          end: LessThanOrEqual(new Date(createJobDto.end)),
        },
      });

      if (existingJobs.length > 0) {
        throw new HttpException(
          'User already has a job scheduled for the same time slot',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    let helpers: User[];

    if (createJobDto.helpers) {
      helpers = await this.usersService.findAllByIds(createJobDto.helpers);

      if (helpers.length !== createJobDto.helpers.length) {
        throw new HttpException(
          'One or more helpers not found',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // Find place
    const place = await this.placesService.findOneById(createJobDto.placeId);

    if (!place) {
      throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
    }

    createJobDto.place = place;
    createJobDto.title = place.title;
    delete createJobDto.placeId;

    let vehicle: Vehicle;

    if (createJobDto.vehicleId) {
      vehicle = await this.vehiclesService.findSeatForUser(
        createJobDto.vehicleId,
        user.id,
        new Date(createJobDto.start),
        new Date(createJobDto.end),
      );

      if (!vehicle) {
        throw new HttpException(
          'Vehicle not available or fully booked',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    //
    if (
      createJobDto.user &&
      createJobDto.place &&
      createJobDto.status === JobStatus.CREATED
    ) {
      createJobDto.status = JobStatus.SCHEDULED;
    }

    // Create job
    const job = this.jobRepository.create({
      ...createJobDto,
      user,
      helpers,
    });

    const savedJob = await this.jobRepository.save(job);

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.CREATE,
      entityType: AuditlogEntityType.JOB,
      entityId: savedJob.id,
      userId: authUserId,
      before: null,
      after: savedJob,
    });

    // Create or update vehicle booking
    let booking: Booking;

    if (vehicle) {
      if (vehicle.bookings && vehicle.bookings.length > 0) {
        booking = await this.vehiclesService.addJobToBooking(
          vehicle.bookings[0].id,
          savedJob.id,
        );
      } else {
        booking = await this.vehiclesService.createBooking({
          vehicle: vehicle.id,
          jobs: [savedJob.id],
          start: createJobDto.start,
          end: createJobDto.end,
        });
      }

      if (!booking) {
        throw new HttpException(
          'Error while creating the booking',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    return this.findOne(savedJob.id);
  }

  async getAlreadyImportedJobTitles() {
    return (
      await this.jobRepository.find({
        select: ['title'],
        where: {
          status: In([JobStatus.CREATED, JobStatus.SCHEDULED]),
          deleted: IsNull(),
        },
        order: { title: 'ASC' },
      })
    ).map((job) => job.title);
  }

  async import(
    file: Express.Multer.File,
    importAll: string = 'false',
    authUserId: string,
  ) {
    const shouldImportAll = importAll === 'true';
    const excel = xlsx.parse(file.buffer, {
      type: 'buffer',
      cellDates: true,
      cellNF: false,
      cellText: false,
      dateNF: 'mm-dd-yyyy',
    });
    const sheet = excel[0].data;
    // const headers = sheet[5];
    const rows = sheet.slice(6);
    const imported = [];

    // Logger.log(`Import jobs | importAll: ${JSON.stringify(importAll)}`);
    // Logger.log(`Import jobs | headers: ${JSON.stringify(headers, null, 2)}`);

    // headers: [
    //   0: "Cleaner",
    //   1: "Same day change-over",
    //   2: "Check in today",
    //   3: "#of Nights",
    //   4: "First Name",
    //   5: "Last Name",
    //   6: "# of adults",
    //   7: "# of children",
    //   8: "Transaction ID #",
    //   9: "Email Address",
    //   10: "Phone",
    //   11: "Property",
    //   12: "Complete date by",
    //   13: "Complete time by",
    //   14: "Notes",
    //   15: "Arrival date",
    //   16: "Arrival day",
    //   17: "Arrival time",
    //   18: "Departure date",
    //   19: "Departure day",
    //   20: "Departure time",
    //   21: "Lead source",
    //   22: "Complete"
    // ]

    const alreadyImportedJobsTitles = await this.getAlreadyImportedJobTitles();

    for (const row of rows) {
      if (!row[11] || !row[18]) {
        continue;
      }

      const placeName = String(row[11].split(' - ')[0]).trim();

      if (!shouldImportAll && alreadyImportedJobsTitles.includes(placeName)) {
        continue;
      }

      const place = await this.placesService.findOneByTitle(placeName);

      // Skip if the place is not found
      if (!place) {
        continue;
      }

      const arrivalDate = row[15];
      const isPastArrival = new Date(arrivalDate) <= new Date();
      const departureDate = row[18];
      const isPastDeparture = new Date(departureDate) <= new Date();
      const estimatedCleaningTime = place.cleaningEstimate || 60;
      const startTime = isPastDeparture ? new Date() : new Date(departureDate);
      const endTime = new Date(
        startTime.getTime() + estimatedCleaningTime * 60000,
      );

      // Skip if the startTime is not a valid date
      if (startTime instanceof Date && !isNaN(startTime.getTime())) {
        try {
          const newJob = this.jobRepository.create({
            title: placeName,
            start: startTime,
            end: endTime,
            status: JobStatus.CREATED,
            place,
          });

          const importedJob = await this.jobRepository.save(newJob);

          // Log activity
          await this.auditlogService.create({
            action: AuditlogAction.IMPORT,
            entityType: AuditlogEntityType.JOB,
            entityId: importedJob.id,
            userId: authUserId,
            before: null,
            after: importedJob,
          });

          // Update place status to dirty if it’s past the departure date and last job was not done since then
          const lastJob = await this.jobRepository.findOne({
            where: {
              place,
              end: MoreThanOrEqual(new Date(departureDate)),
              status: Not(JobStatus.CREATED),
            },
            order: { end: 'DESC' },
          });

          if (!lastJob || new Date(lastJob.end) <= new Date(departureDate)) {
            if (
              (isPastArrival || isPastDeparture) &&
              place.status !== PlaceStatus.DIRTY
            ) {
              await this.placesService.update(place.id, {
                status: PlaceStatus.DIRTY,
              });
            }
          }

          // if (
          //   (isPastArrival || isPastDeparture) &&
          //   place.status !== PlaceStatus.DIRTY
          // ) {
          //   await this.placesService.update(place.id, {
          //     status: PlaceStatus.DIRTY,
          //   });
          // }

          imported.push(importedJob);
        } catch (error) {
          Logger.error(`Error while importing job: ${error.message}`);
        }
      }
    }

    return imported;
  }

  async findAll(unassigned?: boolean, extended?: boolean, today?: string) {
    const startDate = today ? new Date(Date.parse(today)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = today ? new Date(today) : new Date();
    endDate.setHours(23, 59, 59, 999);

    const all = await this.jobRepository.find({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        color: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        helpers: { id: true, firstName: true, lastName: true },
        extraSheets: true,
        cleaningType: true,
        verifyCleaning: true,
        cleaningVerified: true,
        cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
        loggedHours: true,
        place: extended
          ? {
              id: true,
              title: true,
              description: true,
              status: true,
              image: true,
              cleaningEstimate: true,
              area: {
                name: true,
                colour: true,
                parent: { name: true },
              },
            }
          : { id: true, title: true, area: { colour: true } },
        timelogs: extended
          ? {
              id: true,
              action: true,
              location: true,
              geofenced: true,
              created: true,
              user: { id: true, firstName: true, lastName: true },
            }
          : false,
      },
      where: {
        deleted: IsNull(),
        ...(unassigned && { user: IsNull() }),
        ...(today && {
          start: MoreThanOrEqual(startDate),
          end: LessThanOrEqual(endDate),
        }),
        status: unassigned ? JobStatus.CREATED : Not(JobStatus.CREATED),
      },
      relations: [
        'user',
        'helpers',
        extended ? 'place.area.parent' : 'place.area',
        'timelogs',
        'timelogs.user',
        'booking',
        'booking.vehicle',
        'cleaningVerifiedBy',
      ],
      order: {
        ...(unassigned && {
          place: {
            area: {
              parent: { name: 'ASC' },
              name: 'ASC',
            },
          },
        }),
        // title: 'ASC',
        start: 'DESC',
      },
    });

    if (extended) {
      all.forEach((job) => {
        if (!job.place || !job.place.cleaningEstimate) {
          return;
        }

        const estimatedCleaningTime = job.place.cleaningEstimate || 60;
        const startLog = job.timelogs.find(
          (log) => log.action === TimelogAction.START,
        );
        const stopLog = job.timelogs.find(
          (log) => log.action === TimelogAction.STOP,
        );

        if (startLog && stopLog) {
          const loggedHours =
            (stopLog.created.getTime() - startLog.created.getTime()) / 3600000; // Milliseconds to hours
          job.loggedHours = loggedHours;
        }

        const loggedMinutes = job.loggedHours * 60;
        const delta = Math.abs(estimatedCleaningTime - loggedMinutes);
        const deltaInMinutes = loggedMinutes - estimatedCleaningTime;
        const deltaInPercentage =
          (deltaInMinutes / estimatedCleaningTime) * 100;
        const deltaSign = deltaInMinutes > 0 ? '+' : '';

        if (delta > estimatedCleaningTime * FlaggableDeltaMargin) {
          job.delta = {
            loggedMinutes: loggedMinutes.toFixed(1),
            deltaInMinutes: deltaSign + deltaInMinutes.toFixed(1),
            deltaInPercentage: deltaSign + deltaInPercentage.toFixed(1),
          };
        }
      });
    }

    if (!unassigned) {
      const driverBookings = await this.getDriverBookingsAsJobs(today);
      all.push(...driverBookings);

      const helperJobs = await this.findAllForHelpers(today);
      all.push(...helperJobs);
    }

    return all;
  }

  async findAllPaginated(
    query: PaginateQuery,
    deltaFlagged?: boolean,
  ): Promise<Paginated<Job>> {
    // If we're filtering by delta, we need to get all jobs first
    // since we can't filter by delta in the database query
    if (deltaFlagged) {
      // Get all jobs with their timelogs
      const allJobs = await this.jobRepository.find({
        select: {
          id: true,
          title: true,
          notes: true,
          start: true,
          end: true,
          status: true,
          color: true,
          created: true,
          updated: true,
          user: { id: true, firstName: true, lastName: true },
          helpers: { id: true, firstName: true, lastName: true },
          extraSheets: true,
          cleaningType: true,
          verifyCleaning: true,
          cleaningVerified: true,
          cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
          loggedHours: true,
          place: {
            id: true,
            title: true,
            cleaningEstimate: true,
            area: { name: true, colour: true, parent: { name: true } },
          },
          timelogs: {
            id: true,
            action: true,
            created: true,
            user: { id: true, firstName: true, lastName: true },
          },
        },
        where: { deleted: IsNull() },
        relations: [
          'user',
          'helpers',
          'place',
          'place.area',
          'place.area.parent',
          'timelogs',
          'timelogs.user',
          'cleaningVerifiedBy',
        ],
      });

      // Calculate delta for each job
      const jobsWithDelta = [];

      allJobs.forEach((job) => {
        if (!job.place?.cleaningEstimate) return;

        const estimatedCleaningTime = job.place.cleaningEstimate || 60;
        const startLog = job.timelogs?.find(
          (log) => log.action === TimelogAction.START,
        );
        const stopLog = job.timelogs?.find(
          (log) => log.action === TimelogAction.STOP,
        );

        if (!startLog || !stopLog) return;

        const loggedHours =
          (stopLog.created.getTime() - startLog.created.getTime()) / 3600000;
        job.loggedHours = loggedHours;

        const loggedMinutes = loggedHours * 60;
        const delta = Math.abs(estimatedCleaningTime - loggedMinutes);

        if (delta > estimatedCleaningTime * FlaggableDeltaMargin) {
          const deltaInMinutes = loggedMinutes - estimatedCleaningTime;
          const deltaInPercentage =
            (deltaInMinutes / estimatedCleaningTime) * 100;
          const deltaSign = deltaInMinutes > 0 ? '+' : '';

          job.delta = {
            loggedMinutes: loggedMinutes.toFixed(1),
            deltaInMinutes: deltaSign + deltaInMinutes.toFixed(1),
            deltaInPercentage: deltaSign + deltaInPercentage.toFixed(1),
          };

          jobsWithDelta.push(job);
        }
      });

      // Create a manual paginated response
      const page = query.page || 1;
      const limit = query.limit || 10;
      const skip = (page - 1) * limit;

      const paginatedData = jobsWithDelta.slice(skip, skip + limit);

      return {
        data: paginatedData,
        meta: {
          itemsPerPage: limit,
          totalItems: jobsWithDelta.length,
          currentPage: page,
          totalPages: Math.ceil(jobsWithDelta.length / limit),
          sortBy: [['start', 'DESC']],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          first: `${query.path}?limit=${limit}`,
          previous:
            page > 1 ? `${query.path}?page=${page - 1}&limit=${limit}` : '',
          current: `${query.path}?page=${page}&limit=${limit}`,
          next:
            page < Math.ceil(jobsWithDelta.length / limit)
              ? `${query.path}?page=${page + 1}&limit=${limit}`
              : '',
          last: `${query.path}?page=${Math.ceil(
            jobsWithDelta.length / limit,
          )}&limit=${limit}`,
        },
      };
    }

    // Standard pagination if not filtering by delta
    return paginate(query, this.jobRepository, {
      sortableColumns: [
        'id',
        'title',
        'start',
        'end',
        'user',
        'status',
        'created',
        'updated',
      ],
      nullSort: 'last',
      defaultSortBy: [['start', 'DESC']],
      searchableColumns: ['title', 'status', 'user.firstName', 'user.lastName'],
      select: [
        'id',
        'title',
        'notes',
        'start',
        'end',
        'status',
        'color',
        'created',
        'updated',
        'user.id',
        'user.firstName',
        'user.lastName',
        'helpers.id',
        'helpers.firstName',
        'helpers.lastName',
        'extraSheets',
        'cleaningType',
        'verifyCleaning',
        'cleaningVerified',
        'cleaningVerifiedBy',
        'loggedHours',
        'place.id',
        'place.title',
        'place.cleaningEstimate',
        'place.area.name',
        'place.area.parent.name',
        'timelogs.id',
        'timelogs.user.id',
        'timelogs.user.firstName',
        'timelogs.user.lastName',
        'timelogs.action',
        'timelogs.created',
        'timelogs.location',
        'timelogs.geofenced',
      ],
      withDeleted: false,
      filterableColumns: {
        status: [FilterOperator.EQ, FilterOperator.IN],
        start: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
        'user.id': [FilterOperator.EQ, FilterOperator.IN],
        'place.id': [FilterOperator.EQ, FilterOperator.IN],
      },
      relations: [
        'user',
        'helpers',
        'place',
        'place.area',
        'place.area.parent',
        'timelogs',
        'timelogs.user',
        'booking',
        'booking.vehicle',
        'cleaningVerifiedBy',
      ],
    });
  }

  async findAllStatusCounts() {
    // Get the count of jobs by status
    const counts = await this.jobRepository
      .createQueryBuilder('job')
      .select('job.status', 'status')
      .addSelect('COUNT(job.id)', 'count')
      .where('job.deleted IS NULL')
      .groupBy('job.status')
      .getRawMany();

    // Get the total count of jobs (excluding deleted)
    const all = await this.jobRepository.count({
      where: { deleted: IsNull() },
    });

    // Get all jobs with timelogs to calculate delta counts
    const jobsWithTimelogs = await this.jobRepository.find({
      select: {
        id: true,
        place: { cleaningEstimate: true },
        loggedHours: true,
        timelogs: { action: true, created: true, user: { id: true } },
      },
      where: {
        deleted: IsNull(),
      },
      relations: ['place', 'timelogs', 'timelogs.user'],
    });

    // Count jobs with significant time deltas
    const deltaCount = jobsWithTimelogs.filter((job) => {
      if (!job.place?.cleaningEstimate) return false;

      const estimatedCleaningTime = job.place.cleaningEstimate;
      const startLog = job.timelogs?.find(
        (log) => log.action === TimelogAction.START,
      );
      const stopLog = job.timelogs?.find(
        (log) => log.action === TimelogAction.STOP,
      );

      if (!startLog || !stopLog) return false;

      const loggedHours =
        (stopLog.created.getTime() - startLog.created.getTime()) / 3600000; // Milliseconds to hours
      const loggedMinutes = loggedHours * 60;
      const delta = Math.abs(estimatedCleaningTime - loggedMinutes);

      return delta > estimatedCleaningTime * FlaggableDeltaMargin;
    }).length;

    // Transform the result into a more readable format
    const statusCounts = counts.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count, 10);
      return acc;
    }, {});

    const valueOrZero = (value: string): number =>
      value ? parseInt(value, 10) : 0;

    // Include the total count and delta count in the response
    return {
      ...statusCounts,
      in_progress:
        valueOrZero(counts[JobStatus.STARTED]) +
        valueOrZero(statusCounts[JobStatus.PAUSED]) +
        valueOrZero(statusCounts[JobStatus.RESUMED]) +
        valueOrZero(statusCounts[JobStatus.DELAYED]),
      completed:
        valueOrZero(statusCounts[JobStatus.DONE]) +
        valueOrZero(statusCounts[JobStatus.INSPECTED]) +
        valueOrZero(statusCounts[JobStatus.CANCELED]),
      all,
      delta_flagged: deltaCount,
    };
  }

  async findAllForHelpers(date?: string) {
    // Helper jobs are jobs that are assigned to a helper
    // Create a new job for each existing job assigned to a helper
    // The new job will be the same as the original job but have the helper as the user

    const startDate = date ? new Date(Date.parse(date)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = date ? new Date(date) : new Date();
    endDate.setHours(23, 59, 59, 999);

    const jobs = await this.jobRepository.find({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        color: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        helpers: { id: true, firstName: true, lastName: true },
        loggedHours: true,
        extraSheets: true,
        cleaningType: true,
        verifyCleaning: true,
        cleaningVerified: true,
        cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
        place: {
          id: true,
          title: true,
          description: true,
          typology: { id: true, name: true },
          status: true,
          image: true,
          address: true,
          location: true,
          cleaningEstimate: true,
          notes: true,
          area: {
            name: true,
            colour: true,
            parent: { name: true },
          },
        },
        // timelogs: { action: true, created: true },
      },
      where: {
        helpers: { id: Not(IsNull()) },
        deleted: IsNull(),
        ...(date && {
          start: MoreThanOrEqual(startDate),
          end: LessThanOrEqual(endDate),
        }),
        status: Not(JobStatus.CREATED),
      },
      relations: [
        'user',
        'helpers',
        'place',
        'place.area',
        'place.area.parent',
        // 'timelogs',
        // 'booking',
        // 'booking.vehicle',
      ],
      order: { start: 'DESC' },
    });

    const helperJobs = [];

    jobs.forEach((job) => {
      job.helpers.forEach((helper) => {
        const helperJob = { ...job };
        helperJob.id = `helper-${job.id}_${helper.id}`;
        helperJob.resourceId = helper.id;
        helperJob.isHelper = true;
        helperJob.editable = false;
        helperJob.eventEditable = false;
        helperJob.resourceEditable = false;

        helperJobs.push(helperJob);
      });
    });

    return helperJobs;
  }

  async findAllDeltas(date?: string) {
    // Find all jobs that have a (30%, positive or negative) delta
    // between the logged hours and the estimated cleaning time, optionally filtered by date

    const startDate = date ? new Date(Date.parse(date)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = date ? new Date(date) : new Date();
    endDate.setHours(23, 59, 59, 999);

    // Update logged hours to be the difference between the start and stop timelogs
    const jobs = await this.jobRepository.find({
      select: {
        id: true,
        start: true,
        end: true,
        status: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        loggedHours: true,
        place: {
          id: true,
          title: true,
          status: true,
          cleaningEstimate: true,
        },
        timelogs: { action: true, created: true },
      },
      where: {
        deleted: IsNull(),
        ...(date && {
          start: MoreThanOrEqual(startDate),
          end: LessThanOrEqual(endDate),
        }),
        status: Not(JobStatus.CREATED),
        timelogs: { action: In([TimelogAction.START, TimelogAction.STOP]) },
      },
      relations: ['user', 'place', 'timelogs'],
      order: { start: 'DESC' },
    });

    // Logger.log(`findAllDeltas | jobs: ${JSON.stringify(jobs, null, 2)}`);

    const deltas = [];

    jobs.forEach((job) => {
      if (!job.place || !job.place.cleaningEstimate) {
        return;
      }

      const estimatedCleaningTime = job.place.cleaningEstimate || 60;
      const startLog = job.timelogs.find(
        (log) => log.action === TimelogAction.START,
      );
      const stopLog = job.timelogs.find(
        (log) => log.action === TimelogAction.STOP,
      );

      if (startLog && stopLog) {
        const loggedHours =
          (stopLog.created.getTime() - startLog.created.getTime()) / 3600000; // Milliseconds to hours
        job.loggedHours = loggedHours;
      }

      const loggedMinutes = job.loggedHours * 60;
      const delta = Math.abs(estimatedCleaningTime - loggedMinutes);
      const deltaInMinutes = loggedMinutes - estimatedCleaningTime;
      // const deltaInHours = Math.abs(deltaInMinutes / 60);
      const deltaInPercentage = (deltaInMinutes / estimatedCleaningTime) * 100;

      if (delta > estimatedCleaningTime * FlaggableDeltaMargin) {
        deltas.push({
          id: job.id,
          title: job.title,
          place: {
            id: job.place.id,
            title: job.place.title,
            estimatedCleaningTime: job.place.cleaningEstimate,
          },
          user: {
            id: job.user.id,
            name: `${job.user.firstName} ${job.user.lastName}`,
          },
          loggedMinutes: loggedMinutes.toFixed(1),
          deltaInMinutes: deltaInMinutes.toFixed(1),
          deltaInPercentage: deltaInPercentage.toFixed(1),
        });
      }
    });

    return deltas;
  }

  async findVerifiable(today?: string) {
    const startDate = today ? new Date(Date.parse(today)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = today ? new Date(today) : new Date();
    endDate.setHours(23, 59, 59, 999);

    const all = await this.jobRepository.find({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        color: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        helpers: { id: true, firstName: true, lastName: true },
        extraSheets: true,
        cleaningType: true,
        verifyCleaning: true,
        cleaningVerified: true,
        cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
        loggedHours: true,
        place: {
          id: true,
          title: true,
          description: true,
          status: true,
          image: true,
          cleaningEstimate: true,
          area: {
            name: true,
            colour: true,
            parent: { name: true },
          },
        },
      },
      where: {
        verifyCleaning: true,
        cleaningVerified: IsNull(),
        deleted: IsNull(),
        ...(today && {
          start: MoreThanOrEqual(startDate),
          end: LessThanOrEqual(endDate),
        }),
      },
      relations: ['user', 'helpers', 'place.area.parent', 'cleaningVerifiedBy'],
      order: {
        // title: 'ASC',
        end: 'ASC',
      },
    });

    return all;
  }

  async getJobsForDriver(userId: string, date?: string) {
    const bookings = await this.vehiclesService.findAllBookingsForDriver(
      userId,
      date,
    );

    const jobs = [];

    bookings.forEach((booking) => {
      booking.jobs.forEach((job) => {
        const driverJob = { ...job };
        driverJob.resourceId = booking.driver.id;
        driverJob.isBooking = true;
        driverJob.editable = false;
        driverJob.eventEditable = false;
        driverJob.resourceEditable = false;
        driverJob.booking = booking;

        jobs.push(driverJob);
      });
    });

    return jobs;
  }

  async getDriverBookingsAsJobs(date?: string) {
    const bookings = await this.vehiclesService.findAllDriverBookings(date);
    const jobs = [];

    bookings.forEach((driving) => {
      const bookingJobsSorted = driving.jobs.sort(
        (a, b) => a.start.getTime() - b.start.getTime(),
      );

      const driverJob = new Job();
      driverJob.id = `booking-${driving.id}`;
      driverJob.title = driving.jobs.map((job) => job.title).join(', ');
      driverJob.status = JobStatus.SCHEDULED;
      driverJob.color = bookingJobsSorted[0].place?.area?.colour;
      driverJob.start = subMinutes(bookingJobsSorted[0].start, 30);
      driverJob.end = addMinutes(
        bookingJobsSorted[bookingJobsSorted.length - 1].end,
        30,
      );
      driverJob.user = driving.driver;
      driverJob.booking = driving;
      driverJob.isBooking = true;
      driverJob.editable = false;
      driverJob.eventEditable = false;
      driverJob.resourceEditable = false;

      jobs.push(driverJob);
    });

    return jobs;
  }

  findAllByIds(ids: string[]) {
    return this.jobRepository.find({
      where: { id: In(ids) },
      relations: ['user', 'helpers'],
    });
  }

  async findByUser(
    userId: string,
    placeId?: string,
    readyToStart?: boolean,
    today?: string,
    driver?: boolean,
  ) {
    const startDate = today ? new Date(Date.parse(today)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = today ? new Date(today) : new Date();
    endDate.setHours(23, 59, 59, 999);

    const baseWhere = {
      deleted: IsNull(),
      ...(placeId && { place: { id: placeId } }),
      ...(readyToStart && {
        start: MoreThanOrEqual(startDate),
        end: LessThanOrEqual(endDate),
        status: In([
          JobStatus.SCHEDULED,
          JobStatus.STARTED,
          JobStatus.PAUSED,
          JobStatus.RESUMED,
        ]),
      }),
      ...(today &&
        !readyToStart && {
          start: MoreThanOrEqual(startDate),
          end: LessThanOrEqual(endDate),
        }),
    };

    const jobsForUser = await this.jobRepository.find({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        color: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        helpers: { id: true, firstName: true, lastName: true },
        loggedHours: true,
        extraSheets: true,
        cleaningType: true,
        verifyCleaning: true,
        cleaningVerified: true,
        cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
        booking: {
          id: true,
          start: true,
          end: true,
          vehicle: { id: true, name: true, plate: true },
          passengers: { id: true, firstName: true, lastName: true },
        },
        place: {
          id: true,
          title: true,
          description: true,
          typology: { id: true, name: true },
          status: true,
          image: true,
          address: true,
          location: true,
          cleaningEstimate: true,
          notes: true,
          area: {
            name: true,
            colour: true,
            parent: { name: true },
          },
        },
        timelogs: {
          id: true,
          action: true,
          created: true,
          user: { id: true, firstName: true, lastName: true },
        },
      },
      relations: [
        'user',
        'helpers',
        'place',
        'place.area',
        'place.area.parent',
        'timelogs.user',
        'booking',
        'booking.vehicle',
        'booking.passengers',
      ],
      where: [
        { ...baseWhere, user: { id: userId } },
        { ...baseWhere, helpers: { id: userId } },
      ],
      order: { start: readyToStart ? 'ASC' : 'DESC' },
    });

    if (driver) {
      const driverBookings = await this.getJobsForDriver(userId, today);
      jobsForUser.push(...driverBookings);
    }

    // Force include helper jobs
    // const helperJobs = await this.findAllForHelpers(today);
    // const helperJobsWithUserId = helperJobs.filter((job) =>
    //   job.helpers.some((helper: User) => helper.id === userId),
    // );
    // jobsForUser.push(...helperJobsWithUserId);

    return jobsForUser;
  }

  async findActiveJobsByUser(userId: string) {
    const baseWhere = {
      status: In([
        JobStatus.SCHEDULED, // Include scheduled jobs to allow helpers
        JobStatus.STARTED,
        JobStatus.PAUSED,
        JobStatus.RESUMED,
      ]),
      deleted: IsNull(),
    };

    let jobs = await this.jobRepository.find({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        helpers: { id: true, firstName: true, lastName: true },
        loggedHours: true,
        cleaningType: true,
        verifyCleaning: true,
        place: {
          id: true,
          title: true,
          description: true,
          typology: { id: true, name: true },
          status: true,
          image: true,
          address: true,
          location: true,
          cleaningEstimate: true,
          notes: true,
          area: {
            name: true,
            colour: true,
            parent: { name: true },
          },
        },
        timelogs: {
          id: true,
          action: true,
          created: true,
          user: { id: true, firstName: true, lastName: true },
        },
      },
      where: [
        {
          ...baseWhere,
          user: { id: userId },
          timelogs: { user: { id: userId } },
        },
        {
          ...baseWhere,
          helpers: { id: userId },
          timelogs: { user: { id: userId } },
        },
      ],
      relations: ['user', 'helpers', 'place.area.parent', 'timelogs.user'],
      order: { start: 'DESC' },
    });

    // Filter out jobs that are not active
    jobs = jobs.filter((job) => {
      const timelogs = job.timelogs;
      const startLogs = timelogs.filter(
        (log) => log.action === TimelogAction.START && log.user.id === userId,
      );
      const stopLogs = timelogs.filter(
        (log) => log.action === TimelogAction.STOP && log.user.id === userId,
      );
      return startLogs.length > 0 && stopLogs.length === 0;
    });

    return jobs;
  }

  findByPlace(placeId: string) {
    return this.jobRepository.find({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        loggedHours: true,
        created: true,
        updated: true,
        user: { id: true, firstName: true, lastName: true },
        helpers: { id: true, firstName: true, lastName: true },
        extraSheets: true,
        cleaningType: true,
        verifyCleaning: true,
        cleaningVerified: true,
        cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
        timelogs: {
          id: true,
          user: { id: true, firstName: true, lastName: true },
          action: true,
          location: true,
          geofenced: true,
          created: true,
          updated: true,
        },
        booking: {
          id: true,
          start: true,
          end: true,
          vehicle: { id: true, name: true, plate: true },
          passengers: { id: true, firstName: true, lastName: true },
          created: true,
          updated: true,
        },
      },
      where: {
        place: { id: placeId },
        deleted: IsNull(),
      },
      relations: [
        'user',
        'helpers',
        'timelogs',
        'timelogs.user',
        'booking',
        'booking.vehicle',
        'booking.passengers',
      ],
      order: { start: 'DESC' },
    });
  }

  findTimelogsForJob(jobId: string) {
    return this.jobRepository.findOne({
      select: {
        id: true,
        timelogs: {
          id: true,
          user: { id: true, firstName: true, lastName: true },
          action: true,
          location: true,
          geofenced: true,
          created: true,
          updated: true,
        },
      },
      where: {
        id: jobId,
        deleted: IsNull(),
      },
      relations: ['timelogs', 'timelogs.user'],
    });
  }

  findOne(id: string) {
    return this.jobRepository.findOne({
      select: {
        id: true,
        title: true,
        notes: true,
        start: true,
        end: true,
        status: true,
        color: true,
        created: true,
        updated: true,
        user: { id: true },
        helpers: { id: true },
        extraSheets: true,
        cleaningType: true,
        verifyCleaning: true,
        cleaningVerified: true,
        cleaningVerifiedBy: { id: true, firstName: true, lastName: true },
        place: {
          id: true,
          title: true,
          description: true,
          typology: { id: true, name: true },
          status: true,
          image: true,
          address: true,
          location: true,
          cleaningEstimate: true,
          notes: true,
          tasks: { title: true, required: true },
          area: {
            id: true,
            name: true,
            colour: true,
            parent: { id: true, name: true },
          },
          tag: {
            serial: true,
          },
        },
        timelogs: {
          id: true,
          action: true,
          created: true,
          user: { id: true, firstName: true, lastName: true },
        },
      },
      where: {
        id,
        deleted: IsNull(),
      },
      relations: [
        'user',
        'helpers',
        'place.area.parent',
        'place.tag',
        'timelogs.user',
      ],
    });
  }

  async update(id: string, jobDto: UpdateJobDto, authUserId?: string) {
    const job = await this.jobRepository.findOne({
      where: { id, deleted: IsNull() },
      relations: ['booking.vehicle'],
    });

    if (!job) {
      throw new HttpException('Job not found', HttpStatus.BAD_REQUEST);
    }

    const before = JSON.stringify(job);

    let user: User;
    if (jobDto.userId) {
      user = await this.usersService.findOneById(jobDto.userId);
      if (!user) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }
    }

    if (jobDto.userId === '') {
      user = null;
    }

    let helpers: User[];
    if (jobDto.helpers) {
      if (jobDto.helpers.length > 0) {
        helpers = await this.usersService.findAllByIds(jobDto.helpers);

        if (helpers.length !== jobDto.helpers.length) {
          throw new HttpException(
            'One or more helpers not found',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        helpers = [];
      }
    }

    let place: Place;
    if (jobDto.placeId) {
      place = await this.placesService.findOneById(jobDto.placeId);
      if (!place) {
        throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
      }
    }

    // Save job so far before vehicle and booking updates
    const updatedJob = await this.jobRepository.save({
      ...job,
      ...jobDto,
      ...(place && place.title && { title: jobDto.title || place.title }),
      user,
      place,
      helpers,
    });

    let vehicle: Vehicle;
    let booking: Booking;

    if (user && jobDto.vehicleId) {
      // Check if the vehicle is available
      if (helpers && helpers.length > 0) {
        vehicle = await this.vehiclesService.findSeatsForUsers(
          jobDto.vehicleId,
          [user.id, ...helpers.map((helper) => helper.id)],
          new Date(jobDto.start),
          new Date(jobDto.end),
        );
      } else {
        vehicle = await this.vehiclesService.findSeatForUser(
          jobDto.vehicleId,
          user.id,
          new Date(jobDto.start),
          new Date(jobDto.end),
        );
      }

      if (!vehicle) {
        throw new HttpException(
          'Vehicle not available or fully booked',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Create or update vehicle booking
      if (vehicle.bookings && vehicle.bookings.length > 0) {
        booking = await this.vehiclesService.addJobToBooking(
          vehicle.bookings[0].id,
          job.id,
        );
      } else {
        booking = await this.vehiclesService.createBooking({
          vehicle: vehicle.id,
          jobs: [job.id],
          start: jobDto.start,
          end: jobDto.end,
        });
      }

      if (!booking) {
        throw new HttpException(
          'Error while creating the booking',
          HttpStatus.BAD_REQUEST,
        );
      }

      // updatedJob.booking = booking;

      // Remove job from the previous booking
      // await this.vehiclesService.removeJobFromBooking(job.booking.id, job.id);

      // Update vehicle
      // updatedJob.booking.vehicle = vehicle;
      booking.vehicle = vehicle;
    }

    if (!jobDto.vehicleId && job.booking) {
      // Remove job from the booking
      await this.vehiclesService.removeJobFromBooking(job.booking.id, job.id);

      // updatedJob.booking = null;
      booking = null;
    }

    // Update status to scheluded if the created job is assigned to a user and a place
    if (user && place && job.status === JobStatus.CREATED) {
      job.status = JobStatus.SCHEDULED;
    }

    const updatedJobWithBooking = await this.jobRepository.save({
      ...updatedJob,
      booking,
    });

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.JOB,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: updatedJobWithBooking ?? updatedJob,
      });
    }

    return this.findOne(id);
  }

  async remove(id: string, authUserId: string) {
    const job = await this.jobRepository.findOneBy({ id });
    if (!job) {
      throw new HttpException('Job not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.JOB,
      entityId: id,
      userId: authUserId,
      before: job,
      after: null,
    });

    return this.jobRepository.softRemove(job);
  }

  async removeImported(authUserId: string) {
    const jobs = await this.jobRepository.find({
      where: {
        user: IsNull(),
        status: JobStatus.CREATED,
      },
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.JOB,
      entityId: jobs.map((job) => job.id).toString(),
      userId: authUserId,
      before: jobs,
      after: null,
    });

    return this.jobRepository.remove(jobs);
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const jobs = await this.jobRepository.findBy({ id: In(ids) });
    jobs.forEach(async (job) => {
      await this.jobRepository.softRemove(job);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.JOB,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: jobs.map((job) => job.id),
      after: null,
    });

    return true;
  }

  async verifyCleaning(id: string, authUserId: string) {
    const job = await this.jobRepository.findOneBy({ id });
    if (!job) {
      throw new HttpException('Job not found', HttpStatus.BAD_REQUEST);
    }

    if (!job.verifyCleaning) {
      throw new HttpException(
        'Job is not marked for cleaning verification',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (job.cleaningVerified) {
      throw new HttpException(
        'Job has already been verified',
        HttpStatus.BAD_REQUEST,
      );
    }

    let user: User;
    if (authUserId) {
      user = await this.usersService.findOneById(authUserId);
      if (!user) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }
    }

    const before = JSON.stringify(job);

    job.cleaningVerified = new Date();
    job.cleaningVerifiedBy = user;
    job.status = JobStatus.INSPECTED;

    const updatedJob = await this.jobRepository.save(job);

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.UPDATE,
      entityType: AuditlogEntityType.JOB,
      entityId: id,
      userId: authUserId,
      before: JSON.parse(before),
      after: updatedJob,
    });

    return updatedJob;
  }
}
