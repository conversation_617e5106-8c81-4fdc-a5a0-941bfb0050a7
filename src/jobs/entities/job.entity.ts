import { DecimalColumnTransformer } from 'src/database/utils';
import { Place } from 'src/places/entities/place.entity';
import { Timelog } from 'src/timelogs/entities/timelog.entity';
import { User } from 'src/users/entities/user.entity';
import { Booking } from 'src/vehicles/entities/booking.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum JobStatus {
  CREATED = 'created',
  SCHEDULED = 'scheduled',
  STARTED = 'started',
  PAUSED = 'paused',
  RESUMED = 'resumed',
  CANCELED = 'canceled',
  DELAYED = 'delayed',
  INSPECTED = 'inspected',
  DONE = 'done',
}

export type IJobDelta = {
  loggedMinutes: string;
  deltaInMinutes: string;
  deltaInPercentage: string;
};

@Entity()
export class Job {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  title: string;

  @Column({ nullable: true })
  notes: string;

  @Column({ type: 'enum', enum: JobStatus, default: JobStatus.CREATED })
  status: string;

  @Column({ nullable: true })
  start: Date;

  @Column({ nullable: true })
  end: Date;

  @Column('decimal', {
    precision: 5,
    scale: 2,
    transformer: new DecimalColumnTransformer(),
    nullable: true,
  })
  loggedHours: number;

  @Column({ nullable: true })
  color: string;

  @ManyToOne(() => User, (user) => user.jobs, { nullable: true })
  user: User;

  @ManyToMany(() => User, (user) => user.helperJobs, { nullable: true })
  @JoinTable()
  helpers: User[];

  @ManyToOne(() => Place, (place) => place.jobs, { nullable: true })
  place: Place;

  @OneToMany(() => Timelog, (timelog) => timelog.job)
  timelogs: Timelog[];

  @ManyToOne(() => Booking, (booking) => booking.jobs, { nullable: true })
  @JoinColumn()
  booking: Booking;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;

  @Column({ default: false })
  extraSheets: boolean;

  @Column({ nullable: true })
  cleaningType: string;

  @Column({ default: false })
  verifyCleaning: boolean;

  @Column({ nullable: true })
  cleaningVerified: Date;

  @ManyToOne(() => User, (user) => user.cleaningVerifications, {
    nullable: true,
  })
  cleaningVerifiedBy: User;

  isBooking: boolean = false;
  isHelper: boolean = false;
  editable: boolean = true;
  eventEditable: boolean = true;
  resourceEditable: boolean = true;
  resourceId: string;
  delta: IJobDelta;
}
