import { User } from 'src/users/entities/user.entity';
import { JobStatus } from '../entities/job.entity';
import { Place } from 'src/places/entities/place.entity';

export class CreateJobDto {
  title?: string;
  notes?: string;
  start: string;
  end: string;
  status?: JobStatus;
  userId: string;
  helpers?: string[];
  user?: User;
  placeId: string;
  place?: Place;
  color?: string;
  vehicleId?: string;
  loggedHours?: number;
  extraSheets?: boolean;
  cleaningType?: string;
}
