import { User } from 'src/users/entities/user.entity';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Job } from '../entities/job.entity';
import { Place } from 'src/places/entities/place.entity';
import { Timelog } from 'src/timelogs/entities/timelog.entity';
import { Booking } from 'src/vehicles/entities/booking.entity';

export class JobDto {
  public readonly id: string;
  public readonly title: string;
  public readonly notes: string;
  public readonly start: Date;
  public readonly end: Date;
  public readonly status: string;
  public readonly user: User;
  public readonly helpers: User[];
  public readonly place: Place;
  public readonly created: Date;
  public readonly updated: Date;
  public readonly deleted: Date;
  public readonly resourceId: string;
  public readonly color: string;
  public readonly colour: string;
  public readonly loggedHours: number;
  public readonly timelogs: Timelog[];
  public readonly booking: Booking;
  public readonly isBooking: boolean;
  public readonly isHelper: boolean;
  public readonly editable: boolean;
  public readonly eventEditable: boolean;
  public readonly resourceEditable: boolean;
  public readonly extraSheets: boolean;
  public readonly delta: IJobD<PERSON>ta;
  public readonly cleaningType: string;
  public readonly verifyCleaning: boolean;
  public readonly cleaningVerified: Date;
  public readonly cleaningVerifiedBy: User;

  constructor(job: Job, withTimelogs = false) {
    if (!job || !job.id) {
      return;
    }

    this.id = job.id;
    this.title = job.title || job.place?.title || `Job ${job.id.split('-')[0]}`;
    this.notes = job.notes;
    this.start = job.start;
    this.end = job.end;
    this.status = job.status;
    this.user = job.user;
    this.helpers = job.helpers;
    this.place = job.place;
    this.created = job.created;
    this.updated = job.updated;
    this.deleted = job.deleted;
    this.resourceId = job.resourceId || job.user?.id;
    this.colour = job.place?.area?.colour || job.color;
    this.color = this.colour;
    this.booking = job.booking;
    this.isBooking = job.isBooking;
    this.isHelper = job.isHelper;
    this.editable = job.editable;
    this.eventEditable = job.eventEditable;
    this.resourceEditable = job.resourceEditable;
    this.extraSheets = job.extraSheets;
    this.delta = job.delta;
    this.cleaningType = job.cleaningType;
    this.verifyCleaning = job.verifyCleaning;
    this.cleaningVerified = job.cleaningVerified;
    this.cleaningVerifiedBy = job.cleaningVerifiedBy;

    if (withTimelogs) {
      this.loggedHours = job.loggedHours;
      this.timelogs = job.timelogs;
    }
  }
}

export function getJobDto(job: Job, withTimelogs = false): JobDto {
  return new JobDto(job, withTimelogs);
}
