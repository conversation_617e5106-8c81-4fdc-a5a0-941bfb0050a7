import { Place } from 'src/places/entities/place.entity';
import { Vehicle } from 'src/vehicles/entities/vehicle.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Area {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  colour: string;

  @ManyToOne(() => Area, (area) => area.parent, { nullable: true })
  parent: Area;

  @OneToMany(() => Place, (place) => place.area, { nullable: true })
  places: Place[];

  @ManyToMany(() => Vehicle, (vehicle) => vehicle.areas, { nullable: true })
  vehicles: Vehicle[];

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
