import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AreasService } from './areas.service';
import { CreateAreaDto } from './dto/create-area.dto';
import { UpdateAreaDto } from './dto/update-area.dto';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';

@Controller('areas')
export class AreasController {
  constructor(private readonly areasService: AreasService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post()
  create(@Body() createAreaDto: CreateAreaDto, @Request() req) {
    return this.areasService.create(createAreaDto, req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll() {
    return this.areasService.findAll();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('search/:name')
  findOneByName(@Param('name') name: string) {
    return this.areasService.findOneByName(name);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.areasService.findOne(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAreaDto: UpdateAreaDto,
    @Request() req,
  ) {
    return this.areasService.update(id, updateAreaDto, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.areasService.remove(id, req.user.userId);
  }
}
