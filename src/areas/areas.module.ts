import { Module } from '@nestjs/common';
import { AreasService } from './areas.service';
import { AreasController } from './areas.controller';
import { DatabaseModule } from 'src/database/database.module';
import { areaProviders } from './area.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...areaProviders, AreasService],
  controllers: [AreasController],
  exports: [AreasService],
})
export class AreasModule {}
