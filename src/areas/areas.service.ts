import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { CreateAreaDto } from './dto/create-area.dto';
import { UpdateAreaDto } from './dto/update-area.dto';
import { repositories } from 'src/common/constants';
import { Repository } from 'typeorm';
import { Area } from './entities/area.entity';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import { ModuleRef } from '@nestjs/core';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';

@Injectable()
export class AreasService implements OnModuleInit {
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.area)
    private areaRepository: Repository<Area>,
  ) {}

  onModuleInit() {
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async create(createAreaDto: CreateAreaDto, authUserId?: string) {
    let parent: Area;

    // Check if the assigned parent area exists
    if (createAreaDto.parent) {
      parent = await this.areaRepository.findOneBy({
        id: createAreaDto.parent,
      });

      if (!parent) {
        throw new Error('Parent area does not exist');
      }
    }

    const area = this.areaRepository.create({
      name: createAreaDto.name,
      parent,
      colour: createAreaDto.colour,
    });

    const savedArea = await this.areaRepository.save(area);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.CREATE,
        entityType: AuditlogEntityType.AREA,
        entityId: savedArea.id,
        userId: authUserId,
        before: null,
        after: savedArea,
      });
    }

    return savedArea;
  }

  findAll() {
    return this.areaRepository.find({
      select: {
        id: true,
        name: true,
        colour: true,
        parent: { id: true, name: true },
        places: { id: true },
        created: true,
        updated: true,
      },
      relations: ['parent', 'places'],
      order: {
        parent: { name: 'ASC' },
        name: 'ASC',
      },
    });
  }

  findOne(id: string) {
    return this.areaRepository.findOne({
      select: {
        id: true,
        name: true,
        parent: { id: true, name: true },
        created: true,
        updated: true,
      },
      where: { id },
      relations: ['parent'],
    });
  }

  findOneByName(name: string) {
    return this.areaRepository.findOne({
      select: {
        id: true,
        name: true,
        parent: { id: true, name: true },
        created: true,
        updated: true,
      },
      where: { name },
      relations: ['parent'],
    });
  }

  async update(id: string, updateAreaDto: UpdateAreaDto, authUserId?: string) {
    let parent: Area;

    // Check if the assigned parent area exists
    if (updateAreaDto.parent) {
      parent = await this.areaRepository.findOne({
        where: { id: updateAreaDto.parent },
      });

      if (!parent) {
        throw new Error('Parent area does not exist');
      }
    }

    const area = await this.areaRepository.findOneBy({ id });

    if (!area) {
      throw new Error('Area not found');
    }

    const before = JSON.stringify(area);

    area.name = updateAreaDto.name;
    area.parent = parent;
    area.colour = updateAreaDto.colour;

    const savedArea = await this.areaRepository.save(area);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.AREA,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: savedArea,
      });
    }

    return savedArea;
  }

  async remove(id: string, authUserId: string) {
    const area = await this.areaRepository.findOneBy({ id });
    if (!area) {
      throw new HttpException('Area not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.AREA,
      entityId: id,
      userId: authUserId,
      before: area,
      after: null,
    });

    return this.areaRepository.softRemove({ id });
  }
}
