import { Job } from 'src/jobs/entities/job.entity';
import { User } from 'src/users/entities/user.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum TimelogAction {
  START = 'start',
  PAUSE = 'pause',
  RESUME = 'resume',
  STOP = 'stop',
}

@Entity()
export class Timelog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user) => user.timelogs)
  user: User;

  @ManyToOne(() => Job, (job) => job.timelogs)
  @JoinColumn()
  job: Job;

  @Column({ type: 'enum', enum: TimelogAction, default: TimelogAction.START })
  action: string;

  @Column('point', { nullable: true })
  location: string;

  @Column({ nullable: true })
  geofenced: boolean;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
