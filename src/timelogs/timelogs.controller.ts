import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Request,
  UseGuards,
  Query,
} from '@nestjs/common';
import { TimelogsService } from './timelogs.service';
import { CreateTimelogDto } from './dto/create-timelog.dto';
import { UpdateTimelogDto } from './dto/update-timelog.dto';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { Paginate, PaginateQuery, Paginated } from 'nestjs-paginate';

@Controller('timelogs')
export class TimelogsController {
  constructor(private readonly timelogsService: TimelogsService) {}

  @UseGuards(JwtAccessAuthGuard)
  @Post()
  create(@Request() req, @Body() createTimelogDto: CreateTimelogDto) {
    return this.timelogsService.create(req.user.userId, createTimelogDto);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Patch()
  update(@Request() req, @Body() updateTimelogDto: UpdateTimelogDto) {
    return this.timelogsService.update(req.user.userId, updateTimelogDto);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll() {
    return this.timelogsService.findAll();
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('user/:id')
  findByUser(@Param('id') id: string) {
    return this.timelogsService.findByUser(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('mine')
  findMine(@Request() req) {
    return this.timelogsService.findByUser(req.user.userId);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('staff-hours')
  getStaffHours(
    @Paginate() query: PaginateQuery,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('users') userIds?: string,
  ): Promise<Paginated<any>> {
    // Convert comma-separated userIds to array if provided
    const userIdsArray = userIds ? userIds.split(',') : undefined;
    return this.timelogsService.getStaffHours(
      query,
      startDate,
      endDate,
      userIdsArray,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.timelogsService.findOne(id);
  }
}
