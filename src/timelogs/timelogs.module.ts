import { Module } from '@nestjs/common';
import { TimelogsService } from './timelogs.service';
import { TimelogsController } from './timelogs.controller';
import { DatabaseModule } from 'src/database/database.module';
import { timelogProviders } from './timelog.providers';
import { JobsModule } from 'src/jobs/jobs.module';
import { PlacesModule } from 'src/places/places.module';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [DatabaseModule, JobsModule, PlacesModule, UsersModule],
  providers: [...timelogProviders, TimelogsService],
  controllers: [TimelogsController],
  exports: [TimelogsService],
})
export class TimelogsModule {}
