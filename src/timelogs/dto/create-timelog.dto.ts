import { TimelogAction } from '../entities/timelog.entity';

export class CreateTimelogDto {
  job: string;
  tag: string;
  action?: TimelogAction;
  private _location?: string;

  get location(): string {
    return this._location || null;
  }

  set location(value: string) {
    const [latitude, longitude] = value.split(',');
    if (!latitude || !longitude) {
      throw new Error('Invalid location');
    }

    this._location = `POINT(${latitude.trim()} ${longitude.trim()})`;
  }
}
