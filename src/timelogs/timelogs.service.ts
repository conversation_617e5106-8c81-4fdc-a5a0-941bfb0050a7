import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { CreateTimelogDto } from './dto/create-timelog.dto';
import { UpdateTimelogDto } from './dto/update-timelog.dto';
import { repositories } from 'src/common/constants';
import { Repository } from 'typeorm';
import { Timelog, TimelogAction } from './entities/timelog.entity';
import { JobsService } from 'src/jobs/jobs.service';
import { JobStatus } from 'src/jobs/entities/job.entity';
import { UpdateJobDto } from 'src/jobs/dto/update-job.dto';
import { UpdatePlaceDto } from 'src/places/dto/update-place.dto';
import { PlaceStatus } from 'src/places/entities/place.entity';
import { PlacesService } from 'src/places/places.service';
import { isPointWithinRadius } from 'geolib';
import { UsersService } from 'src/users/users.service';
// import { ModuleRef } from '@nestjs/core';
import {
  // FilterOperator,
  // paginate,
  Paginated,
  PaginateQuery,
} from 'nestjs-paginate';

const geofenceRadius = 100;

@Injectable()
export class TimelogsService implements OnModuleInit {
  private userService: UsersService;

  constructor(
    // private moduleRef: ModuleRef,
    @Inject(repositories.timelog)
    private timelogRepository: Repository<Timelog>,
    private jobsService: JobsService,
    private placesService: PlacesService,
    private usersService: UsersService,
  ) {}

  onModuleInit() {
    // We can remove this if we're directly injecting UsersService
    // this.userService = this.moduleRef.get(UsersService, { strict: false });
  }

  // Create timelog AKA start job
  async create(userId: string, createTimelogDto: CreateTimelogDto) {
    // Check if job exists
    const job = await this.jobsService.findOne(createTimelogDto.job);

    if (!job) {
      throw new HttpException('Job not found', HttpStatus.BAD_REQUEST);
    }

    const user = await this.userService.findOneById(userId);

    if (!user) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }

    // Check if the NFC tag matches the place tag for this job
    if (
      job.place.tag &&
      createTimelogDto.tag.trim().toUpperCase() !==
        job.place.tag?.serial?.trim().toUpperCase()
    ) {
      throw new HttpException(
        'Tag does not match the place for this job',
        HttpStatus.BAD_REQUEST,
      );
    }

    const isJobOwner = job.user.id === userId;
    const isJobHelper = job.helpers.some((helper) => helper.id === userId);

    // Check if job belongs to user or user is a helper
    if (!isJobOwner && !isJobHelper) {
      throw new HttpException(
        'User not associated with job',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if no timelog with start action exists for this job
    const startTimelog = await this.timelogRepository.findOne({
      where: { job: { id: job.id }, action: TimelogAction.START },
    });

    if (startTimelog) {
      throw new HttpException('Job already started', HttpStatus.BAD_REQUEST);
    }

    // Check if user location is within the job place geofence
    let geofenced = false;
    if (job.place.location && createTimelogDto.location) {
      const { x: placeLatitude, y: placeLongitude } = job.place.location as any;
      const [userLatitute, userLongitude] = createTimelogDto.location
        .split(',')
        .map((item) => parseFloat(item));

      if (placeLatitude && placeLongitude && userLatitute && userLongitude) {
        geofenced = isPointWithinRadius(
          {
            latitude: userLatitute,
            longitude: userLongitude,
          },
          {
            latitude: placeLatitude,
            longitude: placeLongitude,
          },
          geofenceRadius,
        );
      }
    }

    try {
      // Create timelog
      const timelog = this.timelogRepository.create({
        ...createTimelogDto,
        user,
        job,
        geofenced,
      });

      const savedTimelog = await this.timelogRepository.save(timelog);

      if (isJobOwner) {
        // Update job status
        const jobDto: UpdateJobDto = { status: JobStatus.STARTED };
        await this.jobsService.update(job.id, jobDto, userId);

        // Update job.place status
        const placeDto: UpdatePlaceDto = { status: PlaceStatus.CLEANING };
        await this.placesService.update(job.place.id, placeDto);
      }

      return savedTimelog;
    } catch (error) {
      throw new HttpException(
        'Could not create timelog',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  findAll() {
    return this.timelogRepository.find({
      select: {
        id: true,
        user: { id: true, firstName: true, lastName: true },
        job: {
          id: true,
          title: true,
          start: true,
          end: true,
          status: true,
          loggedHours: true,
          place: { id: true, title: true, description: true, status: true },
        },
        action: true,
        location: true,
        geofenced: true,
        created: true,
        updated: true,
      },
      relations: ['user', 'job.place'],
      order: { created: 'DESC' },
    });
  }

  findByUser(userId: string) {
    return this.timelogRepository.find({
      where: { user: { id: userId } },
      select: {
        id: true,
        user: { id: true, firstName: true, lastName: true },
        job: {
          id: true,
          title: true,
          start: true,
          end: true,
          status: true,
          loggedHours: true,
          place: { id: true, title: true, description: true, status: true },
        },
        action: true,
        location: true,
        geofenced: true,
        created: true,
        updated: true,
      },
      relations: ['user', 'job.place'],
      order: { created: 'DESC' },
    });
  }

  findOne(id: string) {
    return this.timelogRepository.find({
      select: {
        id: true,
        user: { id: true, firstName: true, lastName: true },
        job: {
          id: true,
          title: true,
          start: true,
          end: true,
          status: true,
          loggedHours: true,
          place: { id: true, title: true, description: true, status: true },
        },
        action: true,
        location: true,
        geofenced: true,
        created: true,
        updated: true,
      },
      where: { id },
      relations: ['user', 'job.place'],
    });
  }

  // Update timelog AKA stop job
  async update(userId: string, updateTimelogDto: UpdateTimelogDto) {
    // Check if job exists
    const job = await this.jobsService.findOne(updateTimelogDto.job);

    if (!job) {
      throw new HttpException('Job not found', HttpStatus.BAD_REQUEST);
    }

    const user = await this.userService.findOneById(userId);

    if (!user) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }

    // Check if the NFC tag matches the place tag for this job
    if (
      job.place.tag &&
      updateTimelogDto.tag.trim().toUpperCase() !==
        job.place.tag?.serial?.trim().toUpperCase()
    ) {
      throw new HttpException(
        'Tag does not match the place for this job',
        HttpStatus.BAD_REQUEST,
      );
    }

    const isJobOwner = job.user.id === userId;
    const isJobHelper = job.helpers.some((helper) => helper.id === userId);

    // Check if job belongs to user or user is a helper
    if (!isJobOwner && !isJobHelper) {
      throw new HttpException(
        'User not associated with job',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if the timelog with start action exists for this job
    const startTimelog = await this.timelogRepository.findOne({
      where: { job: { id: job.id }, action: TimelogAction.START },
      order: { created: 'ASC' },
    });

    if (!startTimelog) {
      throw new HttpException('Job not started yet', HttpStatus.BAD_REQUEST);
    }

    // Check if user location is within the job place geofence
    let geofenced = false;
    if (job.place.location && updateTimelogDto.location) {
      const { x: placeLatitude, y: placeLongitude } = job.place.location as any;
      const [userLatitute, userLongitude] = updateTimelogDto.location
        .split(',')
        .map((item) => parseFloat(item));

      if (placeLatitude && placeLongitude && userLatitute && userLongitude) {
        geofenced = isPointWithinRadius(
          {
            latitude: userLatitute,
            longitude: userLongitude,
          },
          {
            latitude: placeLatitude,
            longitude: placeLongitude,
          },
          geofenceRadius,
        );
      }
    }

    try {
      // Create timelog
      const timelog = this.timelogRepository.create({
        ...updateTimelogDto,
        user,
        job,
        geofenced,
      });

      const savedTimelog = await this.timelogRepository.save(timelog);

      if (isJobOwner && updateTimelogDto.action === TimelogAction.STOP) {
        // Update job status
        const loggedTime =
          savedTimelog.created.getTime() - startTimelog.created.getTime();

        const loggedHours = loggedTime / 3600000; // Milliseconds to hours

        const jobDto: UpdateJobDto = {
          status: JobStatus.DONE,
          loggedHours,
        };
        await this.jobsService.update(job.id, jobDto);

        // Update job.place status
        const placeDto: UpdatePlaceDto = { status: PlaceStatus.READY };
        await this.placesService.update(job.place.id, placeDto);
      }

      return savedTimelog;
    } catch (error) {
      throw new HttpException(
        'Could not create timelog',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async getStaffHours(
    query: PaginateQuery,
    startDate?: string,
    endDate?: string,
    userIds?: string[],
  ): Promise<Paginated<any>> {
    // Set default date range to last month if not provided
    const end = endDate ? new Date(endDate) : new Date();
    end.setHours(23, 59, 59, 999);

    const start = startDate ? new Date(startDate) : new Date();
    if (!startDate) {
      // start.setMonth(start.getMonth() - 1); // Default to last month
      start.setMonth(0); // Default to January
      start.setDate(1); // Default to 1st of the month
      start.setHours(0, 0, 0, 0);
    }

    // Build query to get staff with their jobs and timelogs
    let staffWithData;

    // Use the repository through the UsersService
    if (userIds && userIds.length > 0) {
      // If specific users are requested, get them by IDs
      staffWithData = await Promise.all(
        userIds.map((id) => this.usersService.findOneById(id)),
      );
    } else {
      // Otherwise get all staff users
      staffWithData = await this.usersService.findAllStaff();
    }

    // Process data to calculate hours
    const staffHours = [];

    for (const user of staffWithData) {
      if (!user) continue;

      // Get all jobs for this user (both as owner and helper)
      const allJobs = await this.jobsService.findByUser(user.id);

      // Filter jobs within date range and with completed status
      const jobsInRange = allJobs.filter((job) => {
        const jobStart = new Date(job.start);
        const jobEnd = new Date(job.end);
        return (
          jobStart >= start &&
          jobEnd <= end &&
          (job.status === JobStatus.DONE || job.status === JobStatus.INSPECTED)
        );
      });

      // Calculate scheduled hours from jobs
      const scheduledHours = jobsInRange.reduce((total, job) => {
        const jobStart = new Date(job.start);
        const jobEnd = new Date(job.end);
        const hours =
          (jobEnd.getTime() - jobStart.getTime()) / (1000 * 60 * 60);
        return total + hours;
      }, 0);

      // Calculate logged hours from timelogs
      const loggedHours = jobsInRange.reduce((total, job) => {
        return total + (job.loggedHours || 0);
      }, 0);

      if (!scheduledHours && !loggedHours) continue;

      staffHours.push({
        id: user.id,
        user,
        scheduledHours: parseFloat(scheduledHours.toFixed(2)),
        loggedHours: parseFloat(loggedHours.toFixed(2)),
        difference: parseFloat((loggedHours - scheduledHours).toFixed(2)),
        jobCount: jobsInRange.length,
      });
    }

    // Apply sorting to the data
    const sortBy = query.sortBy || [['name', 'ASC']]; // Default sort by user name
    const [sortColumn, sortDirection] = sortBy[0] || ['name', 'ASC'];
    const direction = (sortDirection as 'ASC' | 'DESC') || 'ASC';

    staffHours.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortColumn) {
        case 'name':
          aValue = `${a.user.firstName} ${a.user.lastName}`.toLowerCase();
          bValue = `${b.user.firstName} ${b.user.lastName}`.toLowerCase();
          break;
        case 'scheduled':
          aValue = a.scheduledHours;
          bValue = b.scheduledHours;
          break;
        case 'logged':
          aValue = a.loggedHours;
          bValue = b.loggedHours;
          break;
        case 'difference':
          aValue = a.difference;
          bValue = b.difference;
          break;
        case 'jobs':
          aValue = a.jobCount;
          bValue = b.jobCount;
          break;
        default:
          // Default to user name if unknown column
          aValue = `${a.user.firstName} ${a.user.lastName}`.toLowerCase();
          bValue = `${b.user.firstName} ${b.user.lastName}`.toLowerCase();
      }

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return direction === 'ASC' ? 1 : -1;
      if (bValue == null) return direction === 'ASC' ? -1 : 1;

      // Compare values
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return direction === 'ASC' ? comparison : -comparison;
      } else {
        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return direction === 'ASC' ? comparison : -comparison;
      }
    });

    // Apply pagination to the data
    const limit = query.limit || 10;
    const page = query.page || 1;
    const totalItems = staffHours.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = staffHours.slice(startIndex, endIndex);

    // Create a custom Paginated response
    return {
      data: paginatedData,
      meta: {
        totalItems,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        sortBy: [[sortColumn, direction]],
        searchBy: ['firstName', 'lastName'],
        search: query.search || '',
        select: [
          'id',
          'user',
          'scheduledHours',
          'loggedHours',
          'difference',
          'jobCount',
        ],
        filter: query.filter || {},
      },
      links: {
        first: query.path + `?limit=${limit}&sortBy=${sortColumn}:${direction}`,
        previous:
          page > 1
            ? query.path +
              `?page=${
                page - 1
              }&limit=${limit}&sortBy=${sortColumn}:${direction}`
            : '',
        current:
          query.path +
          `?page=${page}&limit=${limit}&sortBy=${sortColumn}:${direction}`,
        next:
          page < totalPages
            ? query.path +
              `?page=${
                page + 1
              }&limit=${limit}&sortBy=${sortColumn}:${direction}`
            : '',
        last:
          query.path +
          `?page=${totalPages}&limit=${limit}&sortBy=${sortColumn}:${direction}`,
      },
    };
  }
}
