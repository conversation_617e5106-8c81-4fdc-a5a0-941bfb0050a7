import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { jwtConstants } from 'src/common/constants';

@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(
  Strategy,
  jwtConstants.refreshStrategy,
) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.refreshSecret,
      passReqToCallback: true,
    });
  }

  validate(req: Request, payload: any): any {
    const refreshToken = req.headers.authorization.split(' ')[1];
    return {
      userId: payload.sub,
      username: payload.username,
      role: payload.role,
      refreshToken,
    };
  }
}
