import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { jwtConstants } from 'src/common/constants';
import { UsersService } from 'src/users/users.service'; // Add the missing import
@Injectable()
export class JwtAccessStrategy extends PassportStrategy(
  Strategy,
  jwtConstants.accessStrategy,
) {
  constructor(private readonly usersService: UsersService) {
    // Add the missing property
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.accessSecret,
    });
  }

  async validate(payload: any): Promise<any> {
    // TODO: Maybe enrich the payload with more data from the database?
    // TODO: Maybe check if the token.userId is not revoked?

    return {
      userId: payload.sub,
      username: payload.username,
      role: payload.role,
    };
  }
}
