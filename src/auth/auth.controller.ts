import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';

import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAccessAuthGuard } from './guards/jwt-access-auth.guard';
import { JwtRefreshAuthGuard } from './guards/jwt-refresh-auth.guard';
import { SkipAuth } from './decorators/skip-auth.decorator';
import { CreateUserDto } from 'src/users/dto/create-user.dto';
import { PasswordDto } from './dto/password.dto';
import { UpdateUserDto } from 'src/users/dto/update-user.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { fileParser, getDiskStorage, resizeImage } from 'src/common/utils';

const storage = getDiskStorage('./uploads/users');

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @SkipAuth()
  @Post('first-admin')
  createFirstAdmin(@Body() createUserDto: CreateUserDto) {
    return this.authService.createFirstAdmin(createUserDto);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  login(@Request() req) {
    return this.authService.login(req.user);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('me')
  getProfile(@Request() req) {
    return this.authService.getProfile(req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @UseInterceptors(FileInterceptor('photo', { storage }))
  @Patch('me')
  async updateProfile(
    @Request() req,
    @Body() updateUserDto: UpdateUserDto,
    @UploadedFile(fileParser) photo: Express.Multer.File,
  ) {
    const updatedProfile = await this.authService.updateProfile(
      req.user.userId,
      updateUserDto,
      photo,
    );

    if (photo?.path) {
      // Resize the image
      await resizeImage(photo);
    }

    return updatedProfile;
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('logout')
  logout(@Request() req) {
    return this.authService.logout(req.user.userId);
  }

  @UseGuards(JwtRefreshAuthGuard)
  @Get('refresh')
  refreshTokens(@Request() req) {
    return this.authService.refreshTokens(
      req.user.userId,
      req.user.refreshToken,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Patch('password')
  updatePassword(@Request() req, @Body() passwordDto: PasswordDto) {
    return this.authService.updatePassword(req.user.userId, passwordDto);
  }
}
