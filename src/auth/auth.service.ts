import {
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';

import { UsersService } from 'src/users/users.service';
import { jwtConstants } from 'src/common/constants';
import { UserDto, getUserDto } from 'src/users/dto/user.dto';
import { User, UserRole } from 'src/users/entities/user.entity';
import { CreateUserDto } from 'src/users/dto/create-user.dto';
import { PasswordDto } from './dto/password.dto';
import { UpdateUserDto } from 'src/users/dto/update-user.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(username: string, password: string): Promise<any> {
    const user = await this.usersService.findOneByUsername(username);

    if (user) {
      const match = await bcrypt.compare(password, user.password);

      if (match) {
        return user;
      }
    }

    throw new UnauthorizedException();
  }

  async login(user: UserDto): Promise<any> {
    const tokens = await this.getTokens(user.id, user.username, user.role);
    await this.usersService.updateRefreshToken(user.id, tokens.refreshToken);
    // const payload = { sub: user.id, username: user.username };
    const loggedUser = await this.usersService.findOneByUsername(user.username);

    delete loggedUser.password;
    delete loggedUser.refreshToken;

    return {
      ...tokens,
      user: getUserDto(loggedUser),
    };
  }

  async logout(userId: string): Promise<any> {
    await this.usersService.revokeRefreshToken(userId);

    return {
      accessToken: await this.jwtService.signAsync(
        { sub: userId },
        {
          secret: jwtConstants.accessSecret,
          expiresIn: '1ms',
        },
      ),
      refreshToken: await this.jwtService.signAsync(
        { sub: userId },
        {
          secret: jwtConstants.refreshSecret,
          expiresIn: '1ms',
        },
      ),
    };
  }

  async createFirstAdmin(user: CreateUserDto): Promise<User> {
    const firstAdminExists = await this.usersService.findOneByRole(
      UserRole.ADMIN,
    );

    if (firstAdminExists) {
      throw new HttpException(
        'First admin already exists',
        HttpStatus.BAD_REQUEST,
      );
    }

    const userExists = await this.usersService.findOneByUsername(user.username);
    if (userExists) {
      throw new HttpException(
        'User with this username already exists',
        HttpStatus.BAD_REQUEST,
      );
    }

    user.role = UserRole.ADMIN;

    const newUser = await this.usersService.create(user);

    const tokens = await this.getTokens(
      newUser.id,
      newUser.username,
      newUser.role,
    );

    await this.usersService.updateRefreshToken(newUser.id, tokens.refreshToken);

    return {
      ...tokens,
      user: getUserDto(newUser),
    };
  }

  async getProfile(userId: string): Promise<any> {
    const user = await this.usersService.findOneById(userId);

    if (user) {
      return {
        user: getUserDto(user),
      };
    }

    throw new UnauthorizedException();
  }

  async updateProfile(
    userId: string,
    userDto: UpdateUserDto,
    photo: Express.Multer.File,
  ): Promise<any> {
    const user = await this.usersService.findOneById(userId);

    // Remove security related props from userDto
    delete userDto.username;
    delete userDto.role;
    delete userDto.isActive;

    if (user) {
      const updatedUser = await this.usersService.update(
        userId,
        userDto,
        photo,
        userId,
      );

      return {
        user: getUserDto(updatedUser),
      };
    }

    throw new UnauthorizedException();
  }

  async updatePassword(userId: string, password: PasswordDto): Promise<any> {
    const user = await this.usersService.findOneById(userId);

    if (user) {
      const match = await bcrypt.compare(password.old, user.password);

      if (match) {
        await this.usersService.updatePassword(userId, password.new);

        return {
          message: 'Password updated',
        };
      }
    }

    throw new UnauthorizedException();
  }

  async getTokens(
    userdId: string,
    username: string,
    role: string,
  ): Promise<any> {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        { sub: userdId, username, role },
        {
          secret: jwtConstants.accessSecret,
          expiresIn: '3d',
        },
      ),
      this.jwtService.signAsync(
        { sub: userdId, username, role },
        {
          secret: jwtConstants.refreshSecret,
          expiresIn: '7d',
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async refreshTokens(userId: string, refreshToken: string): Promise<any> {
    const user = await this.usersService.findOneById(userId);

    if (!user) {
      throw new UnauthorizedException();
    }

    if (user.refreshToken !== refreshToken) {
      throw new UnauthorizedException();
    }

    const tokens = await this.getTokens(user.id, user.username, user.role);
    await this.usersService.updateRefreshToken(user.id, tokens.refreshToken);

    return {
      ...tokens,
      user: getUserDto(user),
    };
  }
}
