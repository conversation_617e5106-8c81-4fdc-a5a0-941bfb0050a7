import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  UseGuards,
} from '@nestjs/common';
import { BagsService } from './bags.service';
import { CreateBagDto } from './dto/create-bag.dto';
import { UpdateBagDto } from './dto/update-bag.dto';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { RolesGuard } from 'src/auth/guards/roles.guard';

@Controller('bags')
export class BagsController {
  constructor(private readonly bagsService: BagsService) {}

  @UseGuards(JwtAccessAuthGuard)
  @Post()
  create(@Body() createBagDto: CreateBagDto, @Request() req) {
    return this.bagsService.create(createBagDto, req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll(@Request() req) {
    return this.bagsService.findAll(
      req.query?.untagged === 'true',
      req.query?.unassigned === 'true',
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.bagsService.findOne(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateBagDto: UpdateBagDto,
    @Request() req,
  ) {
    return this.bagsService.update(id, updateBagDto, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.bagsService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.bagsService.remove(id, req.user.userId);
  }
}
