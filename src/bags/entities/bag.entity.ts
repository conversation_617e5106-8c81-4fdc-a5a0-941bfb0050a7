import { Place } from 'src/places/entities/place.entity';
import { Tag } from 'src/tags/entities/tag.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum BagStatus {
  PLACE_EMPTY = 'place_empty',
  PLACE_CLEAN = 'place_clean',
  PLACE_DIRTY = 'place_dirty',
  PLACE_TO_LAUNDRY = 'place_to_laundry',
  LAUNDRY_EMPTY = 'laundry_empty',
  LAUNDRY_CLEAN = 'laundry_clean',
  LAUNDRY_DIRTY = 'laundry_dirty',
  LAUNDRY_TO_PLACE = 'laundry_to_place',
}

@Entity()
export class Bag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  notes: string;

  @Column({ type: 'enum', enum: BagStatus, default: BagStatus.PLACE_EMPTY })
  status: BagStatus;

  @OneToOne(() => Tag, (tag) => tag.bag, { nullable: true })
  @JoinColumn()
  tag: Tag;

  @ManyToOne(() => Place, (place) => place.bags, { nullable: true })
  place: Place;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
