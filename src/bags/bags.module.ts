import { Module } from '@nestjs/common';
import { BagsService } from './bags.service';
import { BagsController } from './bags.controller';
import { DatabaseModule } from 'src/database/database.module';
import { bagProviders } from './bags.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...bagProviders, BagsService],
  controllers: [BagsController],
  exports: [BagsService],
})
export class BagsModule {}
