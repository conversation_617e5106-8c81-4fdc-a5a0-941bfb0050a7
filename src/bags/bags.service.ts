import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { CreateBagDto } from './dto/create-bag.dto';
import { UpdateBagDto } from './dto/update-bag.dto';
import { repositories } from 'src/common/constants';
import { In, IsNull, Repository } from 'typeorm';
import { Bag } from './entities/bag.entity';
import { TagsService } from 'src/tags/tags.service';
import { Tag } from 'src/tags/entities/tag.entity';
import { Place } from 'src/places/entities/place.entity';
import { PlacesService } from 'src/places/places.service';
import { ModuleRef } from '@nestjs/core';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';

@Injectable()
export class BagsService implements OnModuleInit {
  private tagsService: TagsService;
  private placesService: PlacesService;
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.bag)
    private bagRepository: Repository<Bag>,
  ) {}

  onModuleInit() {
    this.tagsService = this.moduleRef.get(TagsService, { strict: false });
    this.placesService = this.moduleRef.get(PlacesService, { strict: false });
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async create(createBagDto: CreateBagDto, authUserId?: string) {
    try {
      let tag: Tag;
      let place: Place;

      if (createBagDto.tag) {
        // Find tag
        tag = await this.tagsService.findSerial(createBagDto.tag);

        if (tag) {
          // Check if tag is already linked to an object
          if (tag.isLinked) {
            throw new HttpException(
              'Tag is already linked to an object',
              HttpStatus.BAD_REQUEST,
            );
          }
        } else {
          // Create tag if it does not exist
          tag = await this.tagsService.create({ serial: createBagDto.tag });
        }

        if (createBagDto.place) {
          // Find place
          place = await this.placesService.findOneById(createBagDto.place);

          if (!place) {
            throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
          }
        }

        const bag = this.bagRepository.create({
          ...createBagDto,
          tag,
          place,
        });

        const savedBag = await this.bagRepository.save(bag);

        // Log activity
        if (authUserId) {
          await this.auditlogService.create({
            action: AuditlogAction.CREATE,
            entityType: AuditlogEntityType.BAG,
            entityId: savedBag.id,
            userId: authUserId,
            before: null,
            after: savedBag,
          });
        }

        return savedBag;
      } else {
        throw new HttpException('Tag is required', HttpStatus.BAD_REQUEST);
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  findAll(untagged?: boolean, unassigned?: boolean) {
    return this.bagRepository.find({
      select: {
        id: true,
        notes: true,
        status: true,
        created: true,
        updated: true,
        tag: { id: true, serial: true },
        place: {
          id: true,
          title: true,
          area: {
            id: true,
            name: true,
            parent: { id: true, name: true },
          },
        },
      },
      where: {
        ...(untagged && { tag: IsNull() }),
        ...(unassigned && { place: IsNull() }),
      },
      relations: ['tag', 'place.area.parent'],
    });
  }

  findOne(id: string) {
    return this.bagRepository.findOne({
      select: {
        id: true,
        notes: true,
        status: true,
        created: true,
        updated: true,
        tag: { id: true, serial: true },
        place: {
          id: true,
          title: true,
          image: true,
          area: {
            id: true,
            name: true,
            parent: { id: true, name: true },
          },
        },
      },
      where: { id },
      relations: ['tag', 'place.area.parent'],
    });
  }

  async update(id: string, updateBagDto: UpdateBagDto, authUserId?: string) {
    try {
      const bag = await this.bagRepository.findOne({ where: { id } });

      if (!bag) {
        throw new HttpException('Bag not found', HttpStatus.BAD_REQUEST);
      }

      const before = JSON.stringify(bag);

      const { notes, status } = updateBagDto;

      bag.notes = notes;
      bag.status = status;
      const savedBag = await this.bagRepository.save(bag);

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.UPDATE,
          entityType: AuditlogEntityType.BAG,
          entityId: id,
          userId: authUserId,
          before: JSON.parse(before),
          after: savedBag,
        });
      }

      return this.findOne(id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  async remove(id: string, authUserId: string) {
    const bag = await this.bagRepository.findOne({ where: { id } });

    if (!bag) {
      throw new HttpException('Bag not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.BAG,
      entityId: id,
      userId: authUserId,
      before: bag,
      after: null,
    });

    return this.bagRepository.softRemove(bag);
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const bags = await this.bagRepository.findBy({ id: In(ids) });
    bags.forEach(async (bag) => {
      await this.bagRepository.softRemove(bag);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.BAG,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: bags.map((bag) => bag.id),
      after: null,
    });

    return true;
  }
}
