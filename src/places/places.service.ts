import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import { In, Repository } from 'typeorm';
import xlsx from 'node-xlsx';
import { CreatePlaceDto } from './dto/create-place.dto';
import { UpdatePlaceDto } from './dto/update-place.dto';
import { repositories } from 'src/common/constants';
import { Place, PlaceStatus } from './entities/place.entity';
import { TagsService } from 'src/tags/tags.service';
import { Typology } from './entities/typology.entity';
import { AreasService } from 'src/areas/areas.service';
import { UpdateTypologyDto } from './dto/update-typology.dto';
import { CreateTypologyDto } from './dto/create-typology.dto';
import { Area } from 'src/areas/entities/area.entity';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import { ModuleRef } from '@nestjs/core';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';
import { UpdatePlacesDto } from './dto/update-places.dto';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class PlacesService implements OnModuleInit {
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.place)
    private placeRepository: Repository<Place>,
    @Inject(repositories.typology)
    private typologyRepository: Repository<Typology>,
    private tagsService: TagsService,
    private areasService: AreasService,
    private usersService: UsersService,
  ) {}

  onModuleInit() {
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async findOne(id: string): Promise<Place | undefined> {
    return this.findOneById(id);
  }

  async findOneById(id: string): Promise<Place | undefined> {
    return this.placeRepository.findOne({
      select: {
        id: true,
        title: true,
        description: true,
        typology: { id: true, name: true },
        status: true,
        image: true,
        address: true,
        location: true,
        cleaningEstimate: true,
        notes: true,
        isActive: true,
        created: true,
        updated: true,
        tag: { id: true, serial: true },
        area: {
          id: true,
          name: true,
          colour: true,
          parent: { id: true, name: true },
        },
        tasks: { title: true, required: true },
        octorateId: true,
        owner: { id: true, firstName: true, lastName: true },
      },
      where: { id },
      relations: ['tag', 'area', 'area.parent', 'typology', 'owner'],
    });
  }

  async findOneByTitle(title: string): Promise<Place | undefined> {
    return this.placeRepository.findOne({
      select: {
        id: true,
        title: true,
        description: true,
        typology: { id: true, name: true },
        status: true,
        image: true,
        address: true,
        location: true,
        cleaningEstimate: true,
        notes: true,
        isActive: true,
        created: true,
        updated: true,
        tag: { id: true, serial: true },
        area: {
          id: true,
          name: true,
          colour: true,
          parent: { id: true, name: true },
        },
        tasks: { title: true, required: true },
        octorateId: true,
        owner: { id: true, firstName: true, lastName: true },
      },
      where: { title },
      relations: ['tag', 'area', 'area.parent', 'typology', 'owner'],
    });
  }

  async findJobsByPlace(id: string): Promise<Place | undefined> {
    return this.placeRepository.findOne({
      select: {
        id: true,
        title: true,
        description: true,
        jobs: {
          id: true,
          title: true,
          notes: true,
          start: true,
          end: true,
          status: true,
          loggedHours: true,
          created: true,
          updated: true,
          user: { id: true, firstName: true, lastName: true },
          timelogs: {
            id: true,
            user: { id: true, firstName: true, lastName: true },
            action: true,
            location: true,
            geofenced: true,
            created: true,
            updated: true,
          },
          booking: {
            id: true,
            start: true,
            end: true,
            vehicle: { id: true, name: true, plate: true },
            passengers: { id: true, firstName: true, lastName: true },
            created: true,
            updated: true,
          },
        },
      },
      where: { id },
      relations: [
        'jobs.user',
        'jobs.timelogs.user',
        'jobs.booking.vehicle',
        'jobs.booking.passengers',
      ],
      order: { jobs: { start: 'DESC' } },
    });
  }

  async create(
    createPlaceDto: CreatePlaceDto,
    image?: Express.Multer.File,
    authUserId?: string,
  ): Promise<Place> {
    const place = await this.findOneByTitle(createPlaceDto.title);

    if (place) {
      throw new HttpException(
        'Place with this title already exists',
        HttpStatus.BAD_REQUEST,
      );
    }

    let tag = null;

    // Avoid duplicate tags
    if (createPlaceDto.tag) {
      // Check if tag exists and if it’s assigned to a place
      const tagExists = await this.tagsService.findSerial(createPlaceDto.tag);

      if (tagExists) {
        if (tagExists.isLinked) {
          throw new HttpException(
            'Tag already assigned to another object',
            HttpStatus.BAD_REQUEST,
          );
        } else {
          tag = tagExists;
        }
      }
    }

    if (!tag && createPlaceDto.tag) {
      // Create tag
      tag = await this.tagsService.create({
        serial: createPlaceDto.tag,
      });
    }

    let area = null;

    if (createPlaceDto.area) {
      // Check if area exists
      area = await this.areasService.findOne(createPlaceDto.area);

      if (!area) {
        throw new HttpException('Area not found', HttpStatus.BAD_REQUEST);
      }
    }

    let typology = null;

    if (createPlaceDto.typology) {
      // Check if typology exists
      typology = await this.typologyRepository.findOneBy({
        id: createPlaceDto.typology,
      });

      if (!typology) {
        throw new HttpException('Typology not found', HttpStatus.BAD_REQUEST);
      }
    }

    let owner = null;

    if (createPlaceDto.owner) {
      // Check if owner exists
      owner = await this.usersService.findOneById(createPlaceDto.owner);

      if (!owner) {
        throw new HttpException('Owner not found', HttpStatus.BAD_REQUEST);
      }
    }

    // Prevent error when location is not provided
    if (!createPlaceDto.location) {
      delete createPlaceDto.location;
    }

    // Prevent error when estimate is not provided
    if (!createPlaceDto.cleaningEstimate) {
      delete createPlaceDto.cleaningEstimate;
    }

    // Prevent error when status is not provided
    if (!createPlaceDto.status) {
      delete createPlaceDto.status;
    }

    const newPlace = this.placeRepository.create({
      ...createPlaceDto,
      tag,
      area,
      typology,
      owner,
      image: image ? '/' + image.path : null,
      isActive: true,
    });

    const savedPlace = await this.placeRepository.save(newPlace);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.CREATE,
        entityType: AuditlogEntityType.PLACE,
        entityId: savedPlace.id,
        userId: authUserId,
        before: null,
        after: savedPlace,
      });
    }

    return savedPlace;
  }

  async createTypology(
    typologyDto: CreateTypologyDto,
    authUserId?: string,
  ): Promise<Typology> {
    const savedTypology = await this.typologyRepository.save(typologyDto);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.CREATE,
        entityType: AuditlogEntityType.TYPOLOGY,
        entityId: savedTypology.id,
        userId: authUserId,
        before: null,
        after: savedTypology,
      });
    }

    return savedTypology;
  }

  async import(
    file: Express.Multer.File,
    authUserId: string,
  ): Promise<Place[]> {
    const excel = xlsx.parse(file.buffer);
    const sheet = excel[0].data;
    const headers = sheet[0];
    const rows = sheet.slice(1);

    Logger.log(
      `Importing places | headers: ${JSON.stringify(headers, null, 2)}`,
    );
    // Logger.log(`Importing places | rows: ${JSON.stringify(rows, null, 2)}`);

    // headers
    // [
    //   0: "title",
    //   1: "typology (name)",
    //   2: "description",
    //   3: "address",
    //   4: "location",
    //   5: "cleaningEstimate",
    //   6: "(parent) area (name)",
    //   7: "area (name)",
    //   8: "notes"
    // ]

    const imported: Place[] = [];

    rows.forEach(async (row) => {
      if (!row[0]) {
        return;
      }

      const titleValue = row[0];
      const typologyValue = row[1] ? String(row[1]).trim() : null;
      const descriptionValue = row[2] ? String(row[2]).trim() : null;
      const addressValue = row[3] ? String(row[3]).trim() : null;
      const locationValue = row[4] ? String(row[4]).trim() : null;
      const cleaningEstimateValue = row[5]
        ? parseInt(String(row[5]).trim(), 10)
        : null;
      const parentAreaValue = row[6] ? String(row[6]).trim() : null;
      const childAreaValue = row[7] ? String(row[7]).trim() : null;
      const notesValue = row[8] ? String(row[8]).trim() : null;

      const place = await this.findOneByTitle(titleValue);

      if (place) {
        Logger.log(`Place ${row[0]} already exists`);
        imported.push(place);
      } else {
        // prepare typology
        let typology: Typology;

        // check if typology exists
        typology = await this.typologyRepository.findOneBy({
          name: typologyValue,
        });

        if (!typology) {
          // create typology
          typology = await this.typologyRepository.save({
            name: typologyValue,
          });
        }

        // prepare area
        let area: Area;

        // if the area and sub-area names are the same, it means it's a parent area
        if (parentAreaValue === childAreaValue) {
          // check if parent area exists
          area = await this.areasService.findOneByName(parentAreaValue);

          if (!area) {
            // create parent area
            area = await this.areasService.create({
              name: parentAreaValue,
            });
          }
        } else {
          // check if child area exists
          area = await this.areasService.findOneByName(childAreaValue);

          if (!area) {
            // check if parent area exists
            let parent = await this.areasService.findOneByName(parentAreaValue);

            if (!parent) {
              // create parent area
              parent = await this.areasService.create({
                name: parentAreaValue,
              });
            }

            // FIX: Multiple duplicates of the child area are created from the block below

            // create child area
            area = await this.areasService.create({
              name: childAreaValue,
              parent: parent.id,
            });
          }
        }

        const placeDto: CreatePlaceDto = {
          title: titleValue,
          typology: typology ? typology.id : null,
          description: descriptionValue,
          address: addressValue,
          location: locationValue,
          cleaningEstimate: cleaningEstimateValue,
          area: area ? area.id : null,
          notes: notesValue,
          isActive: 'true',
          status: PlaceStatus.READY,
        };

        const newPlace = await this.create(placeDto, null, authUserId);
        imported.push(newPlace);
      }

      return imported;
    });

    return [];
  }

  async importFixLocations(
    file: Express.Multer.File,
    authUserId: string,
  ): Promise<Place[]> {
    const excel = xlsx.parse(file.buffer);
    const sheet = excel[0].data;
    const headers = sheet[0];
    const rows = sheet.slice(1);

    Logger.log(
      `Importing locations of places | headers: ${JSON.stringify(
        headers,
        null,
        2,
      )}`,
    );

    // headers
    // [
    //   0: "title",
    //   1: "typology (name)",
    //   2: "description",
    //   3: "address",
    //   4: "location",
    //   5: "cleaningEstimate",
    //   6: "(parent) area (name)",
    //   7: "area (name)",
    //   8: "notes"
    // ]

    const imported: Place[] = [];

    rows.forEach(async (row) => {
      if (!row[0]) {
        return;
      }

      const titleValue = row[0];
      const locationValue = row[4] ? String(row[4]).trim() : null;

      const place = await this.findOneByTitle(titleValue);

      if (place) {
        const placeDto: UpdatePlaceDto = {
          location: locationValue,
        };

        const updatedPlace = await this.update(
          place.id,
          placeDto,
          null,
          authUserId,
        );

        imported.push(updatedPlace);
      }

      return imported;
    });

    return [];
  }

  findAll(): Promise<Place[]> {
    return this.placeRepository.find({
      select: {
        id: true,
        title: true,
        description: true,
        typology: { id: true, name: true },
        status: true,
        image: true,
        address: true,
        location: true,
        cleaningEstimate: true,
        notes: true,
        isActive: true,
        created: true,
        updated: true,
        tag: { id: true, serial: true },
        area: {
          id: true,
          name: true,
          colour: true,
          parent: { id: true, name: true },
        },
        tasks: { title: true, required: true },
        octorateId: true,
        owner: { id: true, firstName: true, lastName: true },
      },
      relations: ['tag', 'area', 'area.parent', 'typology', 'owner'],
      order: { title: 'ASC' },
    });
  }

  findTypology(id: string): Promise<Typology | undefined> {
    return this.typologyRepository.findOne({
      select: {
        id: true,
        name: true,
        places: { id: true },
        created: true,
        updated: true,
      },
      where: { id },
      relations: ['places'],
    });
  }

  findTypologies(): Promise<Typology[]> {
    return this.typologyRepository.find({
      select: {
        id: true,
        name: true,
        places: { id: true },
        created: true,
        updated: true,
      },
      relations: ['places'],
    });
  }

  async update(
    id: string,
    updatePlaceDto: UpdatePlaceDto,
    image?: Express.Multer.File,
    authUserId?: string,
  ): Promise<Place> {
    const place = await this.findOneById(id);

    if (!place) {
      throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
    }

    const before = JSON.stringify(place);

    let tag = null;

    // Avoid duplicate tags
    if (updatePlaceDto.tag) {
      // Check if tag exists and it’s not assigned to another place
      tag = await this.tagsService.findSerial(updatePlaceDto.tag);

      if (tag && tag.isLinked && tag.id !== place.tag?.id) {
        throw new HttpException(
          'Tag is already linked to an object',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!tag) {
        // Create tag
        tag = await this.tagsService.create({
          serial: updatePlaceDto.tag,
        });
      }
    }

    let area = null;

    if (updatePlaceDto.area) {
      // Check if area exists
      area = await this.areasService.findOne(updatePlaceDto.area);

      if (!area) {
        throw new HttpException('Area not found', HttpStatus.BAD_REQUEST);
      }
    }

    let typology = null;

    if (updatePlaceDto.typology) {
      // Check if typology exists
      typology = await this.typologyRepository.findOneBy({
        id: updatePlaceDto.typology,
      });

      if (!typology) {
        throw new HttpException('Typology not found', HttpStatus.BAD_REQUEST);
      }
    }

    let owner = null;
    if (updatePlaceDto.owner) {
      // Check if owner exists
      owner = await this.usersService.findOneById(updatePlaceDto.owner);

      if (!owner) {
        throw new HttpException('Owner not found', HttpStatus.BAD_REQUEST);
      }
    }

    // Prevent error when location is not provided
    if (!updatePlaceDto.location) {
      if (place.location) {
        const placeLocation = place.location as any;
        updatePlaceDto.location = `${placeLocation.x}, ${placeLocation.y}`;
      } else {
        delete updatePlaceDto.location;
      }
    }

    // Prevent error when estimate is not provided
    if (!updatePlaceDto.cleaningEstimate) {
      delete updatePlaceDto.cleaningEstimate;
    }

    const updatedPlace = this.placeRepository.merge(place, {
      ...updatePlaceDto,
      tag,
      area,
      typology,
      owner,
      image: image ? '/' + image.path : place.image,
      isActive: updatePlaceDto.isActive
        ? Boolean(updatePlaceDto.isActive)
        : undefined,
    });

    const savedPlace = await this.placeRepository.save(updatedPlace);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.PLACE,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: savedPlace,
      });
    }

    return savedPlace;
  }

  async updateTypology(
    id: string,
    typologyDto: UpdateTypologyDto,
    authUserId?: string,
  ): Promise<Typology> {
    const typology = await this.typologyRepository.findOneBy({ id });
    if (!typology) {
      throw new HttpException('Typology not found', HttpStatus.BAD_REQUEST);
    }

    const savedTypology = this.typologyRepository.save({ id, ...typologyDto });

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.TYPOLOGY,
        entityId: id,
        userId: authUserId,
        before: typology,
        after: savedTypology,
      });
    }

    return savedTypology;
  }

  async remove(id: string, authUserId: string): Promise<Place> {
    const place = await this.findOneById(id);
    if (!place) {
      throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.PLACE,
      entityId: id,
      userId: authUserId,
      before: place,
      after: null,
    });

    return this.placeRepository.softRemove(place);
  }

  async removeTypology(id: string, authUserId: string): Promise<Typology> {
    const typology = this.typologyRepository.findOneBy({ id });
    if (!typology) {
      throw new HttpException('Typology not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.TYPOLOGY,
      entityId: id,
      userId: authUserId,
      before: typology,
      after: null,
    });

    return this.typologyRepository.softRemove({ id });
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const places = await this.placeRepository.findBy({ id: In(ids) });
    places.forEach(async (place) => {
      await this.placeRepository.softRemove(place);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.PLACE,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: places.map((place) => place.id),
      after: null,
    });

    return true;
  }

  async batchUpdate(
    updatePlacesDto: UpdatePlacesDto,
    authUserId: string,
  ): Promise<boolean> {
    const { ids, status } = updatePlacesDto;

    const places = await this.placeRepository.findBy({
      id: In(ids),
    });
    places.forEach(async (place) => {
      // Prevent error when location is not provided
      if (place.location) {
        const placeLocation = place.location as any;
        place.location = `${placeLocation.x}, ${placeLocation.y}`;
      }

      place.status = status;

      // this.placeRepository.merge(place, { status });
      await this.placeRepository.save(place);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.UPDATE,
      entityType: AuditlogEntityType.PLACE,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: places.map((place) => place.id),
      after: null,
    });

    return true;
  }
}
