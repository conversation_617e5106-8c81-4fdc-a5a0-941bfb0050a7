import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

import { PlacesService } from './places.service';
import { CreatePlaceDto } from './dto/create-place.dto';
import { UpdatePlaceDto } from './dto/update-place.dto';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { UserRole } from 'src/users/entities/user.entity';
import { CreateTypologyDto } from './dto/create-typology.dto';
import { UpdateTypologyDto } from './dto/update-typology.dto';
import { fileParser, getDiskStorage, resizeImage } from 'src/common/utils';
import { UpdatePlacesDto } from './dto/update-places.dto';

const storage = getDiskStorage('./uploads/places');

@Controller('places')
export class PlacesController {
  constructor(private readonly placesService: PlacesService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('image', { storage }))
  @Post()
  async create(
    @Body() createPlaceDto: CreatePlaceDto,
    @UploadedFile(fileParser) image: Express.Multer.File,
    @Request() req,
  ) {
    if (image?.path) {
      await resizeImage(image);
    }
    return await this.placesService.create(
      createPlaceDto,
      image,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('file'))
  @Post('import')
  async import(@UploadedFile() file: Express.Multer.File, @Request() req) {
    return await this.placesService.import(file, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('file'))
  @Post('import-locations')
  async importFixLocations(
    @UploadedFile() file: Express.Multer.File,
    @Request() req,
  ) {
    return await this.placesService.importFixLocations(file, req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll() {
    return this.placesService.findAll();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Post('typologies')
  async createTyplogy(@Body() typologyDto: CreateTypologyDto, @Request() req) {
    return await this.placesService.createTypology(
      typologyDto,
      req.user.userId,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('typologies')
  async findTyplogies() {
    return await this.placesService.findTypologies();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('typologies/:id')
  async findTypology(@Param('id') id: string) {
    return await this.placesService.findTypology(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':id/jobs')
  async findJobsForPlace(@Param('id') id: string) {
    return await this.placesService.findJobsByPlace(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.placesService.findOneById(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch('typologies/:id')
  async updateTyplogy(
    @Param('id') id: string,
    @Body() typologyDto: UpdateTypologyDto,
    @Request() req,
  ) {
    return await this.placesService.updateTypology(
      id,
      typologyDto,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch('batch')
  async batchUpdate(
    @Body() updatePlacesDto: UpdatePlacesDto,
    @Request() req,
  ): Promise<boolean> {
    return await this.placesService.batchUpdate(
      updatePlacesDto,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('image', { storage }))
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updatePlaceDto: UpdatePlaceDto,
    @UploadedFile(fileParser) image: Express.Multer.File,
    @Request() req,
  ) {
    if (image?.path) {
      await resizeImage(image);
    }
    return await this.placesService.update(
      id,
      updatePlaceDto,
      image,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('typologies/:id')
  async removeTypology(@Param('id') id: string, @Request() req) {
    return await this.placesService.removeTypology(id, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.placesService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req) {
    return await this.placesService.remove(id, req.user.userId);
  }
}
