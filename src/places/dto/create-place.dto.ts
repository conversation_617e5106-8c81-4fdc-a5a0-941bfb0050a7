import { PlaceStatus } from '../entities/place.entity';

export class CreatePlaceDto {
  title: string;
  description?: string;
  typology: string;
  status: PlaceStatus;
  image?: string;
  tag?: string;
  address: string;
  area?: string;
  private _cleaningEstimate?: number;
  notes?: string;
  isActive?: string;
  private _location?: string;
  tasks?: any;
  octorateId?: string;
  owner?: string;

  get cleaningEstimate(): number {
    return this._cleaningEstimate || null;
  }

  set cleaningEstimate(value: string) {
    if (value.trim() == '' || value == null || value == undefined) {
      this._cleaningEstimate = null;
      return;
    }

    const valueNum = Math.ceil(+value);

    if (valueNum < 0) {
      throw new Error('Invalid cleaning estimate');
    }

    this._cleaningEstimate = valueNum;
  }

  get location(): string {
    return this._location || null;
  }

  set location(value: string) {
    if (value.trim() == '' || value == null || value == undefined) {
      this._location = null;
      return;
    }

    const [latitude, longitude] = value.split(',');
    if (!latitude || !longitude) {
      throw new Error('Invalid location');
    }

    this._location = `POINT(${latitude.trim()} ${longitude.trim()})`;
  }
}
