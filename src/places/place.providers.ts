import { DataSource } from 'typeorm';

import { dataSources, repositories } from 'src/common/constants';
import { Place } from './entities/place.entity';
import { Typology } from './entities/typology.entity';

export const placeProviders = [
  {
    provide: repositories.place,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Place),
    inject: [dataSources.default],
  },
  {
    provide: repositories.typology,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Typology),
    inject: [dataSources.default],
  },
];
