import { Area } from 'src/areas/entities/area.entity';
import { Bag } from 'src/bags/entities/bag.entity';
import { Job } from 'src/jobs/entities/job.entity';
import { Tag } from 'src/tags/entities/tag.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Typology } from './typology.entity';
import { Asset } from 'src/assets/entities/asset.entity';
import { Incident } from 'src/incidents/entities/incident.entity';
import { User } from 'src/users/entities/user.entity';

export enum PlaceStatus {
  READY = 'ready',
  CLEANING = 'cleaning',
  DIRTY = 'dirty',
}

@Entity()
export class Place {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @ManyToOne(() => Typology, (typology) => typology.places)
  typology: Typology;

  @Column({ type: 'enum', enum: PlaceStatus, default: PlaceStatus.READY })
  status: PlaceStatus;

  @OneToOne(() => Tag, (tag) => tag.place, { nullable: true })
  @JoinColumn()
  tag: Tag;

  @Column({ nullable: true })
  image: string;

  @Column({ nullable: true })
  address: string;

  @Column('point', { nullable: true })
  location: string;

  @Column({ nullable: true })
  cleaningEstimate: number;

  @Column({ nullable: true })
  notes: string;

  @Column({ default: true })
  isActive?: boolean;

  @Column({ nullable: true })
  octorateId: string;

  @ManyToOne(() => User, (user) => user.places)
  @JoinColumn()
  owner: User;

  @OneToMany(() => Job, (job) => job.place)
  jobs: Job[];

  @ManyToOne(() => Area, (area) => area.places)
  area: Area;

  @OneToMany(() => Bag, (bag) => bag.place)
  bags: Bag[];

  @OneToMany(() => Asset, (asset) => asset.place)
  assets: Asset[];

  @OneToMany(() => Incident, (incident) => incident.place, { nullable: true })
  incidents: Incident[];

  @Column('jsonb', { nullable: true })
  tasks: Record<string, any>;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
