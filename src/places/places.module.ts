import { Module } from '@nestjs/common';
import { PlacesService } from './places.service';
import { PlacesController } from './places.controller';
import { DatabaseModule } from 'src/database/database.module';
import { placeProviders } from './place.providers';
import { TagsModule } from 'src/tags/tags.module';
import { AreasModule } from 'src/areas/areas.module';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [DatabaseModule, TagsModule, AreasModule, UsersModule],
  providers: [...placeProviders, PlacesService],
  controllers: [PlacesController],
  exports: [PlacesService],
})
export class PlacesModule {}
