import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import 'reflect-metadata';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { PlacesModule } from './places/places.module';
import { JobsModule } from './jobs/jobs.module';
import { TimelogsModule } from './timelogs/timelogs.module';
import { TagsModule } from './tags/tags.module';
import { AreasModule } from './areas/areas.module';
import { BagsModule } from './bags/bags.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { VehiclesModule } from './vehicles/vehicles.module';
import { AssetsModule } from './assets/assets.module';
import { IncidentsModule } from './incidents/incidents.module';
import { AuditlogsModule } from './auditlogs/auditlogs.module';
import { DashboardsModule } from './dashboards/dashboards.module';
import { OctorateModule } from './octorate/octorate.module';
import { CacheInterceptor, CacheModule } from '@nestjs/cache-manager';
import { APP_INTERCEPTOR } from '@nestjs/core';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
      serveRoot: '/uploads',
      serveStaticOptions: {
        index: false,
      },
    }),
    CacheModule.register({
      isGlobal: true,
      ttl: 1000, // 1 second
    }),
    AuthModule,
    UsersModule,
    PlacesModule,
    JobsModule,
    TimelogsModule,
    TagsModule,
    AreasModule,
    BagsModule,
    VehiclesModule,
    AssetsModule,
    IncidentsModule,
    AuditlogsModule,
    DashboardsModule,
    OctorateModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheInterceptor,
    },
  ],
})
export class AppModule {}
