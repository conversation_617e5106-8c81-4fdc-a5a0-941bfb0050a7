import { dataSources } from 'src/common/constants';
import { DataSource } from 'typeorm';

export const databaseProviders = [
  {
    provide: dataSources.default,
    useFactory: async () => {
      const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT, 10) || 5433,
        ssl:
          process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
        username: process.env.DB_USER,
        password: process.env.DB_PASS,
        database: process.env.DB_NAME,
        entities: [`${__dirname}/../**/*.entity{.ts,.js}`],
        // synchronize: process.env.NODE_ENV === 'development' ? true : false,
        synchronize: false,
        // migrations: [`${__dirname}/../migrations/*{.ts,.js}`],
        migrations: ['dist/migrations/*{.ts,.js}'],
        migrationsRun: false,
        logging: process.env.NODE_ENV === 'development' ? true : false,
      });

      return dataSource.initialize();
    },
  },
];
