import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { VehiclesService } from './vehicles.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { CreateBookingDto } from './dto/create-booking.dto';

@Controller('vehicles')
export class VehiclesController {
  constructor(private readonly vehiclesService: VehiclesService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post()
  create(@Body() createVehicleDto: CreateVehicleDto, @Request() req) {
    return this.vehiclesService.create(createVehicleDto, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post('bookings')
  createBooking(@Body() createBookingDto: CreateBookingDto, @Request() req) {
    return this.vehiclesService.createBooking(
      createBookingDto,
      req.user.userId,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll(@Request() req) {
    return this.vehiclesService.findAll(
      req.query?.available === 'true',
      req.query?.area,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('bookings')
  findAllBookings(@Request() req) {
    return this.vehiclesService.findAllBookings(req.query?.date);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('drivings')
  findAllDriverBookings(@Request() req) {
    return this.vehiclesService.findAllDriverBookings(req.query?.date);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('bookings/:id')
  findOneBooking(@Param('id') id: string) {
    return this.vehiclesService.findOneBooking(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.vehiclesService.findOne(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id/bookings')
  findAllBookingsForVehicle(@Param('id') id: string) {
    return this.vehiclesService.findAllBookingsForVehicle(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch('bookings/:id')
  updateBooking(
    @Param('id') id: string,
    @Body() updateBookingDto: UpdateBookingDto,
    @Request() req,
  ) {
    return this.vehiclesService.updateBooking(
      id,
      updateBookingDto,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateVehicleDto: UpdateVehicleDto,
    @Request() req,
  ) {
    return this.vehiclesService.update(id, updateVehicleDto, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.vehiclesService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('bookings/:id')
  removeBooking(@Param('id') id: string, @Request() req) {
    return this.vehiclesService.removeBooking(id, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.vehiclesService.remove(id, req.user.userId);
  }
}
