import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Booking } from './booking.entity';
import { Area } from 'src/areas/entities/area.entity';

@Entity()
export class Vehicle {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  plate: string;

  @Column()
  seats: number;

  @Column({ nullable: true })
  colour: string;

  @Column({ default: true })
  active?: boolean;

  @OneToMany(() => Booking, (booking) => booking.vehicle)
  bookings: Booking[];

  @ManyToMany(() => Area, (area) => area.vehicles, { nullable: true })
  @JoinTable()
  areas: Area[];

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
