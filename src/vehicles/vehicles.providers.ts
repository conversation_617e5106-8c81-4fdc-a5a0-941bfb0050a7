import { DataSource } from 'typeorm';

import { dataSources, repositories } from 'src/common/constants';
import { Vehicle } from './entities/vehicle.entity';
import { Booking } from './entities/booking.entity';

export const vehicleProviders = [
  {
    provide: repositories.vehicle,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Vehicle),
    inject: [dataSources.default],
  },
  {
    provide: repositories.booking,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Booking),
    inject: [dataSources.default],
  },
];
