import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { repositories } from 'src/common/constants';
import {
  In,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import { Vehicle } from './entities/vehicle.entity';
import { Booking } from './entities/booking.entity';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { CreateBookingDto } from './dto/create-booking.dto';
import { JobsService } from 'src/jobs/jobs.service';
import { ModuleRef } from '@nestjs/core';
import { AreasService } from 'src/areas/areas.service';
import { Area } from 'src/areas/entities/area.entity';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';
import { UsersService } from 'src/users/users.service';
import { User, UserRole } from 'src/users/entities/user.entity';
import { Job } from 'src/jobs/entities/job.entity';

@Injectable()
export class VehiclesService implements OnModuleInit {
  private jobsService: JobsService;
  private areasService: AreasService;
  private auditlogService: AuditlogsService;
  private userService: UsersService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.vehicle)
    private vehicleRepository: Repository<Vehicle>,
    @Inject(repositories.booking)
    private bookingRepository: Repository<Booking>,
  ) {}

  onModuleInit() {
    this.jobsService = this.moduleRef.get(JobsService, { strict: false });
    this.areasService = this.moduleRef.get(AreasService, { strict: false });
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
    this.userService = this.moduleRef.get(UsersService, { strict: false });
  }

  async create(createVehicleDto: CreateVehicleDto, authUserId?: string) {
    let areas: Area[] = [];
    if (createVehicleDto.areas && createVehicleDto.areas.length > 0) {
      areas = await Promise.all(
        createVehicleDto.areas.map((area) => this.areasService.findOne(area)),
      );
    }

    const vehicle = this.vehicleRepository.create({
      ...createVehicleDto,
      areas,
    });

    const savedVehicle = await this.vehicleRepository.save(vehicle);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.CREATE,
        entityType: AuditlogEntityType.VEHICLE,
        entityId: savedVehicle.id,
        userId: authUserId,
        before: null,
        after: savedVehicle,
      });
    }

    return savedVehicle;
  }

  async createBooking(createBookingDto: CreateBookingDto, authUserId?: string) {
    // Check if vehicle is provided and exists
    const vehicle = await this.findOne(createBookingDto.vehicle);

    if (!vehicle) {
      throw new HttpException('Vehicle not found', HttpStatus.NOT_FOUND);
    }

    const startDate = new Date(createBookingDto.start);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(createBookingDto.end);
    endDate.setHours(23, 59, 59, 999);

    // Check if vehicle is available for the booking
    const existingBookings = await this.findAllBookingsForVehicle(
      vehicle.id,
      startDate,
      endDate,
    );

    if (existingBookings && existingBookings.length > 0) {
      throw new HttpException(
        'Vehicle not available or fully booked',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if jobs are provided and exist
    const jobs = await this.jobsService.findAllByIds(createBookingDto.jobs);

    if (!jobs || jobs.length === 0) {
      throw new HttpException('Jobs are required', HttpStatus.BAD_REQUEST);
    }

    // Get the passengers from the jobs
    const passengers = jobs
      .map((job) => ([job.user, job.helpers] as any).flat())
      .reduce((acc, user) => {
        if (!acc.find((u: User) => u.id === user.id)) {
          acc.push(user);
        }
        return acc;
      }, [])
      .flat();

    Logger.log('Passengers:', JSON.stringify(passengers, null, 2));

    if (!passengers || passengers.length === 0) {
      throw new HttpException(
        'Passengers are required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (passengers.length > vehicle.seats) {
      throw new HttpException(
        'Passengers exceed the number of seats',
        HttpStatus.BAD_REQUEST,
      );
    }

    const booking = this.bookingRepository.create({
      ...createBookingDto,
      start: startDate,
      end: endDate,
      vehicle,
      passengers,
      jobs,
    });

    const savedBooking = await this.bookingRepository.save(booking);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.CREATE,
        entityType: AuditlogEntityType.BOOKING,
        entityId: savedBooking.id,
        userId: authUserId,
        before: null,
        after: savedBooking,
      });
    }

    return savedBooking;
  }

  findAll(availableOnly?: boolean, area?: string) {
    return this.vehicleRepository.find({
      relations: ['areas'],
      where: {
        ...(availableOnly && { active: true }),
        ...(area && { areas: [{ id: In([area]) }, { id: IsNull() }] }),
        deleted: IsNull(),
      },
    });
  }

  findAllBookings(date?: string) {
    const startDate = date ? new Date(Date.parse(date)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = date ? new Date(date) : new Date();
    endDate.setHours(23, 59, 59, 999);

    return this.bookingRepository.find({
      where: {
        start: MoreThanOrEqual(startDate),
        end: LessThanOrEqual(endDate),
        deleted: IsNull(),
      },
      relations: [
        'vehicle',
        'passengers',
        'driver',
        'jobs.place.area.parent',
        'jobs.helpers',
      ],
    });
  }

  findAllBookingsForVehicle(id: string, start?: Date, end?: Date) {
    return this.bookingRepository.find({
      select: {
        id: true,
        start: true,
        end: true,
        passengers: { id: true, firstName: true, lastName: true },
        driver: { id: true, firstName: true, lastName: true },
        jobs: {
          id: true,
          title: true,
          status: true,
          user: { id: true, firstName: true, lastName: true },
        },
        created: true,
        updated: true,
      },
      relations: ['passengers', 'jobs'],
      where: {
        vehicle: { id },
        start: MoreThanOrEqual(start || new Date()),
        end: LessThanOrEqual(end || new Date()),
        deleted: IsNull(),
      },
    });
  }

  findAllDriverBookings(date?: string) {
    const startDate = date ? new Date(date) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = date ? new Date(date) : new Date();
    endDate.setHours(23, 59, 59, 999);

    return this.bookingRepository.find({
      where: {
        start: MoreThanOrEqual(startDate),
        end: LessThanOrEqual(endDate),
        driver: { role: UserRole.DRIVER },
        deleted: IsNull(),
      },
      relations: ['driver', 'jobs.place.area'],
    });
  }

  findAllBookingsForDriver(userId: string, date?: string) {
    const startDate = date ? new Date(date) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = date ? new Date(date) : new Date();
    endDate.setHours(23, 59, 59, 999);

    return this.bookingRepository.find({
      where: {
        start: MoreThanOrEqual(startDate),
        end: LessThanOrEqual(endDate),
        driver: { id: userId },
        deleted: IsNull(),
      },
      relations: [
        'driver',
        'vehicle',
        'jobs.user',
        'jobs.helpers',
        'jobs.place.area',
      ],
    });
  }

  findOne(id: string) {
    return this.vehicleRepository.findOneBy({ id });
  }

  async findSeatForUser(
    id: string,
    userId: string,
    start: Date,
    end: Date,
  ): Promise<Vehicle & { bookings?: Booking[] }> {
    const startDate = new Date(start);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(end);
    endDate.setHours(23, 59, 59, 999);

    // Check if the vehicle exists and is active
    const vehicle = await this.vehicleRepository.findOne({
      where: { id, active: true },
    });

    if (!vehicle) {
      return null;
    }

    // Check if the vehicle has bookings on the day
    const bookings = await this.findAllBookingsForVehicle(
      id,
      startDate,
      endDate,
    );

    if (!bookings || bookings.length === 0) {
      return vehicle;
    }

    // Check if the user has already booked as a seat
    const userBookings = bookings.filter((booking) =>
      booking.passengers.find((passenger) => passenger.id === userId),
    );

    if (userBookings.length > 0) {
      return { ...vehicle, bookings: userBookings };
    }

    const driverSeatTaken =
      bookings.filter((booking) => booking.driver).length > 0 ? 1 : 0;

    const availableSeats =
      vehicle.seats -
      driverSeatTaken -
      bookings.reduce((acc, booking) => {
        acc += booking.passengers.length;
        return acc;
      }, 0);

    if (availableSeats > 0) {
      return { ...vehicle, bookings };
    }

    return vehicle;
  }

  async findSeatsForUsers(
    id: string,
    userIds: string[],
    start: Date,
    end: Date,
  ): Promise<Vehicle & { bookings?: Booking[] }> {
    const startDate = new Date(start);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(end);
    endDate.setHours(23, 59, 59, 999);

    // Check if the vehicle exists and is active
    const vehicle = await this.vehicleRepository.findOne({
      where: { id, active: true, deleted: IsNull() },
    });

    if (!vehicle) {
      return null;
    }

    // Check if the vehicle has bookings on the day
    const bookings = await this.findAllBookingsForVehicle(
      id,
      startDate,
      endDate,
    );

    if (!bookings || bookings.length === 0) {
      return vehicle;
    }

    // Check if the users have already booked seats
    const userBookings = bookings.filter((booking) =>
      booking.passengers.find((passenger) => userIds.includes(passenger.id)),
    );

    Logger.log(
      `@ findSeatsForUsers() userBookings: ${JSON.stringify(
        userBookings,
        null,
        2,
      )}`,
    );

    if (userBookings.length > 0) {
      return { ...vehicle, bookings: userBookings };
    }

    const driverSeatTaken =
      bookings.filter((booking) => booking.driver).length > 0 ? 1 : 0;

    const availableSeats =
      vehicle.seats -
      driverSeatTaken -
      bookings.reduce((acc, booking) => {
        acc += booking.passengers.length;
        return acc;
      }, 0);

    Logger.log(`@ findSeatsForUsers() availableSeats: ${availableSeats}`);

    if (availableSeats >= userIds.length) {
      return { ...vehicle, bookings };
    }

    return vehicle;
  }

  findAllAvailable(start: Date, end: Date) {
    return this.vehicleRepository.find({
      where: {
        active: true,
        bookings: {
          start: MoreThanOrEqual(start),
          end: LessThanOrEqual(end),
        },
      },
    });
  }

  findOneBooking(id: string, relations?: string[]) {
    return this.bookingRepository.findOne({
      where: { id, deleted: IsNull() },
      relations,
    });
  }

  async update(
    id: string,
    updateVehicleDto: UpdateVehicleDto,
    authUserId?: string,
  ) {
    const vehicle = await this.findOne(id);

    if (!vehicle) {
      throw new HttpException('Vehicle not found', HttpStatus.NOT_FOUND);
    }

    const before = JSON.stringify(vehicle);

    let areas = [];

    if (updateVehicleDto.areas && updateVehicleDto.areas.length > 0) {
      areas = await Promise.all(
        updateVehicleDto.areas.map((area) => this.areasService.findOne(area)),
      );
    }

    const updatedVehicle = this.vehicleRepository.merge(vehicle, {
      ...updateVehicleDto,
      areas,
    });

    const savedVehicle = await this.vehicleRepository.save(updatedVehicle);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.VEHICLE,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: savedVehicle,
      });
    }

    return savedVehicle;
  }

  async updateBooking(
    id: string,
    updateBookingDto: UpdateBookingDto,
    authUserId?: string,
  ) {
    const booking = await this.findOneBooking(id, [
      'jobs',
      'jobs.user',
      'jobs.helpers',
      'passengers',
      'vehicle',
    ]);

    if (!booking) {
      throw new HttpException('Booking not found', HttpStatus.NOT_FOUND);
    }

    const before = JSON.stringify(booking);

    if (!updateBookingDto.vehicle) {
      throw new HttpException('Vehicle is required', HttpStatus.BAD_REQUEST);
    }

    // Update the vehicle
    const vehicle = await this.findOne(updateBookingDto.vehicle);

    if (!vehicle) {
      throw new HttpException('Vehicle not found', HttpStatus.NOT_FOUND);
    }

    booking.vehicle = vehicle;

    // Update the driver
    if (updateBookingDto.driver) {
      const driver = await this.userService.findOneById(
        updateBookingDto.driver,
      );

      if (!driver) {
        throw new HttpException('Driver not found', HttpStatus.BAD_REQUEST);
      }

      booking.driver = driver;
    }

    // Update the jobs
    if (!updateBookingDto.jobs || updateBookingDto.jobs.length === 0) {
      throw new HttpException('Jobs are required', HttpStatus.BAD_REQUEST);
    }

    const jobs = await this.jobsService.findAllByIds(
      updateBookingDto.jobs.filter((id) => !id.startsWith('helper-')),
    );

    if (!jobs || jobs.length === 0) {
      throw new HttpException('Jobs not found', HttpStatus.BAD_REQUEST);
    }

    booking.jobs = jobs;

    // Update the passengers
    const passengers = this.getPassengers(jobs);

    if (booking.driver && !passengers.find((p) => p.id === booking.driver.id)) {
      passengers.push(booking.driver);
    }

    Logger.log('@ updateBooking | Passengers:', passengers);

    if (passengers.length > vehicle.seats) {
      throw new HttpException(
        'Passengers exceed the number of seats',
        HttpStatus.BAD_REQUEST,
      );
    }

    booking.passengers = passengers;

    // Update notes
    booking.notes = updateBookingDto.notes;

    const savedBooking = await this.bookingRepository.save(booking);

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.BOOKING,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: savedBooking,
      });
    }

    return savedBooking;
  }

  async addJobToBooking(bookingId: string, jobId: string) {
    const booking = await this.findOneBooking(bookingId, [
      'jobs',
      'jobs.user',
      'jobs.helpers',
      'passengers',
      'vehicle',
    ]);

    const job = await this.jobsService.findOne(jobId);

    if (!booking || !job) {
      throw new HttpException(
        'Booking or job not found',
        HttpStatus.BAD_REQUEST,
      );
    }

    const vehicle = await this.findOne(booking.vehicle.id);

    const uniqueJobs = booking.jobs.filter((j) => j.id !== job.id);

    const newJobsList = [...uniqueJobs, job];
    booking.jobs = newJobsList;

    const passengers = this.getPassengers(newJobsList);

    if (passengers.length > vehicle.seats) {
      throw new HttpException(
        'Passengers exceed the number of seats',
        HttpStatus.BAD_REQUEST,
      );
    }

    booking.passengers = passengers;

    Logger.log(
      `@ addJobToBooking() | passengers: ${JSON.stringify(passengers)}`,
    );

    return this.bookingRepository.save(booking);
  }

  async removeJobFromBooking(bookingId: string, jobId: string) {
    const booking = await this.findOneBooking(bookingId, [
      'jobs.user',
      'passengers',
    ]);

    if (!booking) {
      throw new HttpException('Booking not found', HttpStatus.NOT_FOUND);
    }

    Logger.log(
      '@ RemoveJob() Booking BEFORE',
      JSON.stringify(booking, null, 2),
    );

    const jobIndex = booking.jobs.findIndex((job) => job.id === jobId);

    // Remove the job
    if (jobIndex > -1) {
      booking.jobs.splice(jobIndex, 1);
    }

    // Update the passengers
    booking.passengers = booking.jobs
      .map((job) => ([job.user, job.helpers] as any).flat())
      .reduce((acc, user) => {
        if (!acc.find((u: User) => u.id === user.id)) {
          acc.push(user);
        }
        return acc;
      }, [])
      .flat();

    Logger.log('@ RemoveJob() Booking AFTER', JSON.stringify(booking, null, 2));

    // Remove the booking if no jobs are left
    if (booking.jobs.length === 0) {
      return this.removeBooking(bookingId);
    }

    return this.bookingRepository.save(booking);
  }

  async remove(id: string, authUserId?: string) {
    const vehicle = this.findOne(id);
    if (!vehicle) {
      throw new HttpException('Vehicle not found', HttpStatus.NOT_FOUND);
    }

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.DELETE,
        entityType: AuditlogEntityType.VEHICLE,
        entityId: id,
        userId: authUserId,
        before: vehicle,
        after: null,
      });
    }

    return this.vehicleRepository.softRemove({ id });
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const vehicles = await this.vehicleRepository.findBy({ id: In(ids) });
    vehicles.forEach(async (job) => {
      await this.vehicleRepository.softRemove(job);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.VEHICLE,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: vehicles.map((vehicle) => vehicle.id),
      after: null,
    });

    return true;
  }

  async removeBooking(id: string, authUserId?: string) {
    const booking = this.findOneBooking(id);
    if (!booking) {
      throw new HttpException('Booking not found', HttpStatus.NOT_FOUND);
    }

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.DELETE,
        entityType: AuditlogEntityType.BOOKING,
        entityId: id,
        userId: authUserId,
        before: booking,
        after: null,
      });
    }

    return this.bookingRepository.softRemove({ id });
  }

  getPassengers(jobs: Job[]): User[] {
    // Get the passengers from the jobs
    return jobs.reduce((acc, job) => {
      // Add the main user if it exists
      if (job.user) {
        if (!acc.find((u) => u.id === job.user.id)) {
          acc.push(job.user);
        }
      }

      // Add helpers if they exist
      if (job.helpers && Array.isArray(job.helpers)) {
        job.helpers.forEach((helper) => {
          if (!acc.find((u) => u.id === helper.id)) {
            acc.push(helper);
          }
        });
      }

      return acc;
    }, [] as User[]);
  }
}
