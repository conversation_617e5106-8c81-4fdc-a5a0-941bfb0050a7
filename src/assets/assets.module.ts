import { Module } from '@nestjs/common';
import { AssetsService } from './assets.service';
import { AssetsController } from './assets.controller';
import { DatabaseModule } from 'src/database/database.module';
import { assetProviders } from './assets.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...assetProviders, AssetsService],
  controllers: [AssetsController],
  exports: [AssetsService],
})
export class AssetsModule {}
