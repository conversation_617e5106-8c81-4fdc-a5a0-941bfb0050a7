import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { Tag } from 'src/tags/entities/tag.entity';
import { repositories } from 'src/common/constants';
import { In, IsNull, LessThan, LessThanOrEqual, Repository } from 'typeorm';
import { Asset, AssetStatus } from './entities/asset.entity';
import { TagsService } from 'src/tags/tags.service';
import { ModuleRef } from '@nestjs/core';
import { PlacesService } from 'src/places/places.service';
import { Place } from 'src/places/entities/place.entity';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';
import { CreateAssetRequestDto } from './dto/create-asset-request.dto';
import {
  AssetRequest,
  AssetRequestStatus,
} from './entities/asset-request.entity';
import { UpdateAssetRequestDto } from './dto/update-asset-request.dto';

@Injectable()
export class AssetsService implements OnModuleInit {
  private tagsService: TagsService;
  private placesService: PlacesService;
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.asset)
    private assetRepository: Repository<Asset>,
    @Inject(repositories.assetRequest)
    private assetRequestRepository: Repository<AssetRequest>,
  ) {}

  onModuleInit() {
    this.tagsService = this.moduleRef.get(TagsService, { strict: false });
    this.placesService = this.moduleRef.get(PlacesService, { strict: false });
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async create(
    createAssetDto: CreateAssetDto,
    image?: Express.Multer.File,
    authUserId?: string,
  ) {
    try {
      let tag: Tag = null;
      let place: Place = null;

      if (createAssetDto.tag) {
        // Find tag
        tag = await this.tagsService.findSerial(createAssetDto.tag);

        if (tag && tag.isLinked) {
          // Check if tag is already linked to an object
          throw new HttpException(
            'Tag is already linked to an object',
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!tag) {
          // Create tag if it does not exist
          tag = await this.tagsService.create({ serial: createAssetDto.tag });
        }
      }

      if (createAssetDto.place) {
        // Find place
        place = await this.placesService.findOneById(createAssetDto.place);

        if (!place) {
          throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
        }
      }

      const asset = this.assetRepository.create({
        ...createAssetDto,
        tag,
        place,
        image: image ? '/' + image.path : null,
        active: true,
      });

      const savedAsset = await this.assetRepository.save(asset);

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.CREATE,
          entityType: AuditlogEntityType.ASSET,
          entityId: savedAsset.id,
          userId: authUserId,
          before: null,
          after: savedAsset,
        });
      }

      return this.findOne(asset.id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  async createRequest(
    createAssetRequestDto: CreateAssetRequestDto,
    authUserId?: string,
  ) {
    try {
      if (!createAssetRequestDto.asset) {
        throw new HttpException('Asset is required', HttpStatus.BAD_REQUEST);
      }

      if (!createAssetRequestDto.place) {
        throw new HttpException('Place is required', HttpStatus.BAD_REQUEST);
      }

      if (!createAssetRequestDto.start || !createAssetRequestDto.end) {
        throw new HttpException(
          'Start and end dates are required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const asset = await this.assetRepository.findOne({
        where: { id: createAssetRequestDto.asset },
      });

      if (!asset) {
        throw new HttpException('Asset not found', HttpStatus.BAD_REQUEST);
      }

      if (
        !asset.active ||
        [
          AssetStatus.BROKEN,
          AssetStatus.DISPOSED,
          AssetStatus.LOST,
          AssetStatus.MAINTENANCE,
        ].includes(asset.status)
      ) {
        throw new HttpException(
          'Asset is not available',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (asset.place?.id === createAssetRequestDto.place) {
        throw new HttpException(
          'Asset is already in place',
          HttpStatus.BAD_REQUEST,
        );
      }

      const place = await this.placesService.findOneById(
        createAssetRequestDto.place,
      );

      if (!place) {
        throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
      }

      const request = this.assetRequestRepository.create({
        asset,
        place,
        start: new Date(createAssetRequestDto.start),
        end: new Date(createAssetRequestDto.end),
        notes: createAssetRequestDto.notes,
        status: AssetRequestStatus.PENDING,
        user: { id: authUserId },
      });

      request.start.setHours(0, 0, 0, 0);
      request.end.setHours(23, 59, 59, 999);

      const savedRequest = await this.assetRequestRepository.save(request);

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.CREATE,
          entityType: AuditlogEntityType.ASSET_REQUEST,
          entityId: savedRequest.id,
          userId: authUserId,
          before: null,
          after: savedRequest,
        });
      }

      return savedRequest;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  findAll() {
    return this.assetRepository.find({
      select: {
        id: true,
        name: true,
        description: true,
        notes: true,
        image: true,
        status: true,
        tag: { id: true, serial: true },
        place: {
          id: true,
          title: true,
          area: {
            id: true,
            name: true,
            parent: { id: true, name: true },
          },
        },
        active: true,
        created: true,
        updated: true,
      },
      relations: ['tag', 'place.area.parent'],
    });
  }

  async findAssetsCollectable() {
    const today = new Date();
    today.setHours(23, 59, 59, 999); // end of day

    const assetRequestsEndingToday = await this.assetRequestRepository.find({
      where: {
        end: LessThanOrEqual(today),
        asset: { status: AssetStatus.IN_USE },
        status: AssetRequestStatus.FULFILLED,
      },
    });

    for (const request of assetRequestsEndingToday) {
      if (request.asset) {
        request.asset.status = AssetStatus.COLLECT_PENDING;
        await this.assetRepository.save(request.asset);
      }
    }

    const assetRequestsDelayed = await this.assetRequestRepository.find({
      where: {
        end: LessThan(today),
        asset: {
          status: In([AssetStatus.IN_USE, AssetStatus.COLLECT_PENDING]),
        },
        status: AssetRequestStatus.FULFILLED,
      },
    });

    for (const request of assetRequestsDelayed) {
      if (request.asset) {
        request.asset.status = AssetStatus.COLLECT_DELAYED;
        await this.assetRepository.save(request.asset);
      }
    }

    return this.assetRepository.find({
      select: {
        id: true,
        name: true,
        status: true,
        place: {
          id: true,
          title: true,
        },
      },
      relations: ['place'],
      where: {
        status: In([AssetStatus.COLLECT_DELAYED, AssetStatus.COLLECT_PENDING]),
        active: true,
        deleted: IsNull(),
      },
    });
  }

  findOne(id: string) {
    return this.assetRepository.findOne({
      select: {
        id: true,
        name: true,
        description: true,
        notes: true,
        image: true,
        status: true,
        tag: { id: true, serial: true },
        place: {
          id: true,
          title: true,
          area: {
            id: true,
            name: true,
            parent: { id: true, name: true },
          },
        },
        active: true,
        created: true,
        updated: true,
      },
      where: { id },
      relations: ['tag', 'place.area.parent'],
    });
  }

  findAllRequests() {
    return this.assetRequestRepository.find({
      select: {
        id: true,
        asset: {
          id: true,
          name: true,
          status: true,
          image: true,
          description: true,
        },
        place: {
          id: true,
          title: true,
          area: { id: true, name: true, parent: { id: true, name: true } },
        },
        user: { id: true, firstName: true, lastName: true },
        start: true,
        end: true,
        notes: true,
        status: true,
        created: true,
        updated: true,
      },
      relations: ['asset', 'place.area.parent', 'user'],
    });
  }

  async update(
    id: string,
    updateAssetDto: UpdateAssetDto,
    image?: Express.Multer.File,
    authUserId?: string,
  ) {
    try {
      const asset = await this.assetRepository.findOne({
        where: { id },
        relations: ['tag'],
      });

      if (!asset) {
        throw new HttpException('Asset not found', HttpStatus.BAD_REQUEST);
      }

      const before = JSON.stringify(asset);

      let tag: Tag = null;

      if (updateAssetDto.tag) {
        tag = await this.tagsService.findSerial(updateAssetDto.tag);

        if (tag && tag.isLinked && tag.id !== asset.tag?.id) {
          throw new HttpException(
            'Tag is already linked to an object',
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!tag) {
          tag = await this.tagsService.create({ serial: updateAssetDto.tag });
        }
      }

      let place: Place = null;

      if (updateAssetDto.place) {
        place = await this.placesService.findOneById(updateAssetDto.place);

        if (!place) {
          throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
        }
      }

      const updatedAsset = this.assetRepository.merge(asset, {
        ...updateAssetDto,
        tag,
        place,
        image: image ? '/' + image.path : asset.image,
        active: ['true', 'false'].includes(updateAssetDto.active)
          ? updateAssetDto.active === 'true'
          : asset.active,
      });

      const savedAsset = await this.assetRepository.save(updatedAsset);

      // Update asset request status to fulfilled when asset is delivered to place
      if (
        savedAsset.place !== null &&
        savedAsset.status === AssetStatus.IN_USE
      ) {
        const request = await this.assetRequestRepository.findOne({
          where: { asset: { id }, status: AssetRequestStatus.PENDING },
          relations: ['place'],
          order: { created: 'DESC' },
        });

        const requestBefore = JSON.stringify(request);

        if (request && request.place?.id === savedAsset.place.id) {
          request.status = AssetRequestStatus.FULFILLED;
          await this.assetRequestRepository.save(request);

          // Log activity
          if (authUserId) {
            await this.auditlogService.create({
              action: AuditlogAction.UPDATE,
              entityType: AuditlogEntityType.ASSET_REQUEST,
              entityId: request.id,
              userId: authUserId,
              before: requestBefore,
              after: request,
            });
          }
        }
      }

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.UPDATE,
          entityType: AuditlogEntityType.ASSET,
          entityId: id,
          userId: authUserId,
          before: JSON.parse(before),
          after: savedAsset,
        });
      }

      return this.findOne(id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  async updateRequest(
    id: string,
    updateAssetRequestDto: UpdateAssetRequestDto,
    authUserId?: string,
  ) {
    const request = await this.assetRequestRepository.findOne({
      where: { id },
      relations: ['asset', 'place'],
    });

    if (!request) {
      throw new HttpException('Request not found', HttpStatus.BAD_REQUEST);
    }

    const before = JSON.stringify(request);

    request.status = updateAssetRequestDto.status as AssetRequestStatus;
    await this.assetRequestRepository.save(request);

    // Update asset status when request is fulfilled
    // if (status === AssetRequestStatus.FULFILLED) {
    //   request.asset.status = AssetStatus.IN_USE;
    //   await this.assetRepository.save(request.asset);
    // }

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.ASSET_REQUEST,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: request,
      });
    }

    return this.assetRequestRepository.findOne({
      where: { id },
      relations: ['asset', 'place'],
    });
  }

  async remove(id: string, authUserId: string) {
    const asset = this.assetRepository.findOneBy({ id });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.ASSET,
      entityId: id,
      userId: authUserId,
      before: asset,
      after: null,
    });

    return this.assetRepository.softRemove({ id });
  }

  async removeRequest(id: string, authUserId: string) {
    const assetRequest = this.assetRequestRepository.findOneBy({ id });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.ASSET_REQUEST,
      entityId: id,
      userId: authUserId,
      before: assetRequest,
      after: null,
    });

    return this.assetRequestRepository.softRemove({ id });
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const assets = await this.assetRepository.findBy({ id: In(ids) });
    assets.forEach(async (asset) => {
      await this.assetRepository.softRemove(asset);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.ASSET,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: assets.map((asset) => asset.id),
      after: null,
    });

    return true;
  }
}
