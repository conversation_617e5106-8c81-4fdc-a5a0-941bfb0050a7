import { DataSource } from 'typeorm';

import { dataSources, repositories } from 'src/common/constants';
import { Asset } from './entities/asset.entity';
import { AssetRequest } from './entities/asset-request.entity';

export const assetProviders = [
  {
    provide: repositories.asset,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Asset),
    inject: [dataSources.default],
  },
  {
    provide: repositories.assetRequest,
    useFactory: (dataSource: DataSource) =>
      dataSource.getRepository(AssetRequest),
    inject: [dataSources.default],
  },
];
