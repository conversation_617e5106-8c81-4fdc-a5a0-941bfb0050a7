import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Asset } from './asset.entity';
import { Place } from 'src/places/entities/place.entity';
import { User } from 'src/users/entities/user.entity';

export enum AssetRequestStatus {
  PENDING = 'pending',
  REJECTED = 'rejected',
  FULFILLED = 'fulfilled',
}

@Entity()
export class AssetRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Asset, (asset) => asset.requests)
  asset: Asset;

  @ManyToOne(() => Place)
  place: Place;

  @ManyToOne(() => User)
  user: User;

  @Column()
  start: Date;

  @Column()
  end: Date;

  @Column({ nullable: true })
  notes: string;

  @Column({
    type: 'enum',
    enum: AssetRequestStatus,
    default: AssetRequestStatus.PENDING,
  })
  status: AssetRequestStatus;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
