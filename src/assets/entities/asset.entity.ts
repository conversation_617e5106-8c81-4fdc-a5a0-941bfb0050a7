import { Incident } from 'src/incidents/entities/incident.entity';
import { Place } from 'src/places/entities/place.entity';
import { Tag } from 'src/tags/entities/tag.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AssetRequest } from './asset-request.entity';

export enum AssetStatus {
  IN_STOCK = 'in_stock',
  IN_USE = 'in_use',
  COLLECT_PENDING = 'collect_pending',
  COLLECT_DELAYED = 'collect_delayed',
  MOVING = 'moving',
  MAINTENANCE = 'maintenance',
  LOST = 'lost',
  BROKEN = 'broken',
  DISPOSED = 'disposed',
}

@Entity()
export class Asset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  notes: string;

  @Column({ nullable: true })
  image: string;

  @Column({ type: 'enum', enum: AssetStatus, default: AssetStatus.IN_STOCK })
  status: AssetStatus;

  @Column({ default: true })
  active: boolean;

  @OneToOne(() => Tag, (tag) => tag.asset, { nullable: true })
  @JoinColumn()
  tag: Tag;

  @ManyToOne(() => Place, (place) => place.assets, { nullable: true })
  place: Place;

  @ManyToOne(() => Incident, (incident) => incident.asset, { nullable: true })
  incidents: Incident[];

  @ManyToOne(() => AssetRequest, (request) => request.asset, { nullable: true })
  requests: AssetRequest[];

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;
}
