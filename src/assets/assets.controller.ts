import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

import { AssetsService } from './assets.service';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { fileParser, getDiskStorage, resizeImage } from 'src/common/utils';
import { CreateAssetRequestDto } from './dto/create-asset-request.dto';
import { UpdateAssetRequestDto } from './dto/update-asset-request.dto';

const storage = getDiskStorage('./uploads/assets');

@Controller('assets')
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @UseGuards(JwtAccessAuthGuard)
  @UseInterceptors(FileInterceptor('image', { storage }))
  @Post()
  async create(
    @Body() createAssetDto: CreateAssetDto,
    @UploadedFile(fileParser) image: Express.Multer.File,
    @Request() req,
  ) {
    if (image?.path) {
      await resizeImage(image);
    }
    return this.assetsService.create(createAssetDto, image, req.user.userId);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll() {
    return this.assetsService.findAll();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('collectable')
  findAssetsStatus() {
    return this.assetsService.findAssetsCollectable();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Post('requests')
  createRequest(
    @Body() createAssetRequestDto: CreateAssetRequestDto,
    @Request() req,
  ) {
    return this.assetsService.createRequest(
      createAssetRequestDto,
      req.user.userId,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('requests')
  findAllRequests() {
    return this.assetsService.findAllRequests();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.assetsService.findOne(id);
  }

  @UseGuards(JwtAccessAuthGuard)
  @Patch('requests/:id')
  updateRequest(
    @Param('id') id: string,
    @Body() updateAssetRequestDto: UpdateAssetRequestDto,
    @Request() req,
  ) {
    return this.assetsService.updateRequest(
      id,
      updateAssetRequestDto,
      req.user.userId,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @UseInterceptors(FileInterceptor('image', { storage }))
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAssetDto: UpdateAssetDto,
    @UploadedFile(fileParser) image: Express.Multer.File,
    @Request() req,
  ) {
    if (image?.path) {
      await resizeImage(image);
    }

    return this.assetsService.update(
      id,
      updateAssetDto,
      image,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.assetsService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.assetsService.remove(id, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('requests/:id')
  removeRequest(@Param('id') id: string, @Request() req) {
    return this.assetsService.removeRequest(id, req.user.userId);
  }
}
