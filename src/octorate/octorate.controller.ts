import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { OctorateApiService } from './octorate-api.service';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { UpdateCalendarDto } from './dto/update-calendar.dto';

@Controller('octorate')
export class OctorateController {
  constructor(private apiService: OctorateApiService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('tokens')
  async getTokenDetails() {
    return this.apiService.getTokenDetails();
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('refresh')
  async refreshAccessToken() {
    return this.apiService.refreshAccessToken();
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('places')
  async getAllAccommodations() {
    return this.apiService.getAllAccommodations();
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('place/:id/calendar')
  async getAccommodationCalendar(@Request() req, @Param('id') id: number) {
    return this.apiService.getAccommodationCalendar(id, req.query?.month);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post('place/:id/calendar')
  async updateAccommodationCalendar(
    @Param('id') id: number,
    @Body() dto: UpdateCalendarDto,
  ) {
    return this.apiService.updateAccommodationCalendar(id, dto);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('place/:id')
  async getAccommodationDetails(@Param('id') id: number) {
    return this.apiService.getAccommodationDetails(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER, UserRole.OWNER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('place/:id/statement')
  async getAccommodationStatement(@Request() req, @Param('id') id: number) {
    return this.apiService.getAccommodationStatement(
      id,
      req.query?.startDate,
      req.query?.endDate,
    );
  }
}
