import {
  Injectable,
  HttpException,
  HttpStatus,
  Logger,
  UseInterceptors,
} from '@nestjs/common';
import axios from 'axios';
import { OctorateAuthService } from './octorate-auth.service';
import { ConfigService } from '@nestjs/config';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
import { randomUUID } from 'crypto';
import { UpdateCalendarDto } from './dto/update-calendar.dto';

type OctorateCalendarResponse = {
  data: [
    {
      id: number;
      name: string;
      days: [
        {
          availability: number;
          bookable: boolean;
          closeToArrival: boolean;
          closeToDeparture: boolean;
          cutOffDays: number;
          date: string;
          maxStay: number;
          minStay: number;
          price: number;
          stopSells: number;
        },
      ];
    },
  ];
  page: {
    page: number;
    totalPages: number;
  };
};

type OctorateReservationResponse = {
  data: [
    {
      id: number;
      checkin: string;
      checkout: string;
      firstName: string;
      lastName: string;
      status: string;
      totalGross: number;
      paymentStatus: string;
      totalChildren: number;
      totalGuest: number;
      totalInfants: number;
      touristTax: number;
      accommodation: {
        id: number;
        name: string;
      };
      guests: [
        {
          familyName: string;
          givenName: string;
          ageRange: string;
        },
      ];
    },
  ];
  page: {
    page: number;
    totalPages: number;
  };
};

@Injectable()
export class OctorateApiService {
  constructor(
    private octorateAuthService: OctorateAuthService,
    private configService: ConfigService,
  ) {}

  async getTokenDetails(): Promise<any> {
    const details = await this.octorateAuthService.getTokenDetails();
    return { status: 'success', details };
  }

  async refreshAccessToken(): Promise<any> {
    await this.octorateAuthService.refreshAccessToken();
    const token = await this.octorateAuthService.getAccessToken();
    return { status: 'success', token };
  }

  @CacheTTL(60000) // 1 minute
  // @UseInterceptors(CacheInterceptor)
  async getAccommodationDetails(accommodationId: number): Promise<any> {
    const token = await this.octorateAuthService.getAccessToken();

    Logger.log(`Retrieving details for accommodation ID ${accommodationId}`);

    try {
      const response = await axios.get(
        `${process.env.OCTORATE_BASE_URL}/accommodation/${accommodationId}?fields=id,name,currency,timeZone,timeZoneOffset,phoneNumber,address,latitude,longitude,zipCode,city,checkinStart,checkinEnd,checkout,networkInfo`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        `Failed to retrieve accommodation details: ${error.message}`,
        HttpStatus.BAD_GATEWAY,
      );
    }
  }

  // @UseInterceptors(CacheInterceptor)
  // @CacheTTL(30000) // 30 seconds
  async getAccommodationCalendar(
    accommodationId: number,
    monthYear: string,
  ): Promise<any> {
    const token = await this.octorateAuthService.getAccessToken();
    const [month, year] = monthYear.split('-');

    Logger.log(
      `Retrieving calendar ${month}-${year} for accommodation ID ${accommodationId}`,
    );

    // Validate month and year
    if (!month || !year) {
      throw new HttpException(
        'Invalid month or year format. Expected format: MM-YYYY',
        HttpStatus.BAD_REQUEST,
      );
    }
    const lastDayOfMonth = new Date(Number(year), Number(month), 0).getDate();

    try {
      const calendarResponse = await axios.get<OctorateCalendarResponse>(
        `${process.env.OCTORATE_BASE_URL}/calendar/${accommodationId}?dateFrom=${year}-${month}-01&dateTo=${year}-${month}-${lastDayOfMonth}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );

      const calendarEvents = calendarResponse.data.data
        .map((place) =>
          place.days
            // .filter((day) => day.bookable) // Filter out non-bookable days
            .map((day) => ({
              id: `${place.id}--${randomUUID()}`,
              title: !day.bookable ? '' : 'Available', // Since we're only keeping bookable days
              start: day.date,
              end: day.date,
              allDay: true,
              display: !day.bookable ? 'background' : 'block',
              // display: 'block',
              editable: day.bookable, // Always true since we only have bookable days
              overlap: true,
              extendedProps: {
                place: place.name,
                availability: day.availability,
                price: day.price,
              },
              classNames: !day.bookable ? 'date-unavailable' : 'date-available', // Always 'date-available' since we only have bookable days
            })),
        )
        .flat();

      const reservationResponse = await axios.get<OctorateReservationResponse>(
        `${process.env.OCTORATE_BASE_URL}/reservation/${accommodationId}?startDate=${year}-${month}-01&endDate=${year}-${month}-${lastDayOfMonth}&fields=id,checkin,checkout,firstName,lastName,status,accommodation`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );

      // Expand events with reservation data
      const reservationEvents = reservationResponse.data.data
        .filter(
          (reservation) => reservation.accommodation?.id === accommodationId,
        )
        .map((reservation) => ({
          id: `${accommodationId}--${reservation.id}`,
          // title: 'Booked',
          title: `${reservation.firstName} ${reservation.lastName}`,
          start: reservation.checkin.split('T')[0],
          end: reservation.checkout.split('T')[0],
          allDay: true,
          display: 'block',
          editable: false, // Allow editing if the user is owner
          overlap: true,
          extendedProps: {
            place: reservation.accommodation?.name || 'Unknown',
            availability: 0, // Assuming 0 for booked days
            price: reservation.totalGross || 0,
          },
          classNames: 'date-booked', // Always 'date-booked' for reservations
        }));

      // Combine calendar and reservation events
      return [...calendarEvents, ...reservationEvents];
    } catch (error) {
      throw new HttpException(
        `Failed to retrieve accommodation calendar: ${error.message}`,
        HttpStatus.BAD_GATEWAY,
      );
    }
  }

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(60000) // 1 minute
  async getAllAccommodations(): Promise<any> {
    const token = await this.octorateAuthService.getAccessToken();
    const networkId = this.configService.get<string>('OCTORATE_NETWORK_ID');

    Logger.log('Retrieving all accommodations');

    try {
      const response = await axios.get(
        `${process.env.OCTORATE_BASE_URL}/accommodation/network?accommodationId=${networkId}&excludeCurrent=true`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        `Failed to retrieve all accommodations: ${error.message}`,
        HttpStatus.BAD_GATEWAY,
      );
    }
  }

  async updateAccommodationCalendar(
    accommodationId: number,
    dto: UpdateCalendarDto,
  ): Promise<any> {
    const token = await this.octorateAuthService.getAccessToken();

    Logger.log(`Updating calendar for accommodation ID ${accommodationId}`);

    try {
      const response = await axios.post(
        `${process.env.OCTORATE_BASE_URL}/calendar/bulk`,
        [
          {
            room: accommodationId,
            dateFrom: dto.date,
            dateTo: dto.date,
            values: {
              availability: dto.availability,
            },
          },
        ],
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        `Failed to update accommodation calendar: ${error.message}`,
        HttpStatus.BAD_GATEWAY,
      );
    }
  }

  // @UseInterceptors(CacheInterceptor)
  // @CacheTTL(30000) // 30 seconds
  async getAccommodationStatement(
    accommodationId: number,
    startDate: string,
    endDate: string,
  ): Promise<any> {
    const token = await this.octorateAuthService.getAccessToken();

    Logger.log(
      `Retrieving statement for accommodation ID ${accommodationId} between ${startDate} and ${endDate} `,
    );

    try {
      const reservationResponse = await axios.get<OctorateReservationResponse>(
        `${process.env.OCTORATE_BASE_URL}/reservation/${accommodationId}?startDate=${startDate}&endDate=${endDate}&fields=id,checkin,checkout,firstName,lastName,status,accommodation,guests,totalGuest,totalChildren,totalInfants,totalGross,roomGross,paymentStatus,touristTax`,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );

      // Expand events with reservation data
      const reservations = reservationResponse.data.data
        .filter(
          (reservation) => reservation.accommodation?.id === accommodationId,
        )
        .map((reservation) => ({
          id: reservation.id,
          guest: `${reservation.firstName} ${reservation.lastName}`,
          start: reservation.checkin,
          end: reservation.checkout,
          nights:
            Math.ceil(
              (new Date(reservation.checkout.replace('[UTC]', '')).getTime() -
                new Date(reservation.checkin.replace('[UTC]', '')).getTime()) /
                (1000 * 60 * 60 * 24),
            ) || 0,
          place: {
            id: reservation.accommodation?.id,
            name: reservation.accommodation?.name || 'Unknown',
          },
          totalGross: reservation.totalGross || 0,
          status: reservation.status,
          paymentStatus: reservation.paymentStatus,
          totalChildren: reservation.totalChildren || 0,
          totalGuests: reservation.totalGuest || 0,
          totalInfants: reservation.totalInfants || 0,
          touristTax: reservation.touristTax || 0,
        }));

      return reservations;
    } catch (error) {
      throw new HttpException(
        `Failed to retrieve accommodation statement: ${error.message}`,
        HttpStatus.BAD_GATEWAY,
      );
    }
  }
}
