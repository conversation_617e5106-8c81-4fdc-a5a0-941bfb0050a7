// src/auth/auth.service.ts
import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

enum AuthKeys {
  ACCESS_TOKEN = 'OCTORATE_ACCESS_TOKEN',
  REFRESH_TOKEN = 'OCTORATE_REFRESH_TOKEN',
  ACCESS_EXPIRES_AT = 'OCTORATE_ACCESS_EXPIRES_AT',
  CLIENT_ID = 'OCTORATE_CLIENT_ID',
  CLIENT_SECRET = 'OCTORATE_CLIENT_SECRET',
  BASE_URL = 'OCTORATE_BASE_URL',
}

@Injectable()
export class OctorateAuthService {
  private accessToken: string;
  private refreshToken: string;
  private tokenExpiresAt: number;

  private clientID: string;
  private clientSecret: string;
  private baseUrl: string;

  constructor(private configService: ConfigService) {
    this.accessToken = this.configService.get<string>(AuthKeys.ACCESS_TOKEN);
    this.refreshToken = this.configService.get<string>(AuthKeys.REFRESH_TOKEN);
    this.tokenExpiresAt = this.parseExpiryDate(
      this.configService.get<string>(AuthKeys.ACCESS_EXPIRES_AT),
    );

    this.clientID = this.configService.get<string>(AuthKeys.CLIENT_ID);
    this.clientSecret = this.configService.get<string>(AuthKeys.CLIENT_SECRET);
    this.baseUrl = this.configService.get<string>(AuthKeys.BASE_URL);
  }

  // Helper method to parse the expiry date
  private parseExpiryDate(dateString: string): number {
    if (!dateString) {
      Logger.error('Invalid date string: null or undefined');
      return NaN;
    }

    // Remove the [UTC] part if it exists
    const cleanedDateString = dateString.replace('[UTC]', '');

    const date = new Date(cleanedDateString);
    if (isNaN(date.getTime())) {
      Logger.error(`Invalid date string: ${cleanedDateString}`);
      return NaN;
    }

    return Math.floor(date.getTime() / 1000);
  }

  // Returns a valid access token, refreshing if it's near expiry
  async getAccessToken(): Promise<string> {
    const now = Math.floor(Date.now() / 1000);

    if (!this.accessToken) {
      Logger.log('@ getAccessToken() No access token available');
      throw new HttpException(
        'No access token available',
        HttpStatus.UNAUTHORIZED,
      );
    }

    Logger.log('@ getAccessToken() Current time:', now);
    Logger.log('@ getAccessToken() Token expires at:', this.tokenExpiresAt);

    if (!this.tokenExpiresAt || isNaN(this.tokenExpiresAt)) {
      this.tokenExpiresAt = this.parseExpiryDate(
        this.configService.get<string>(AuthKeys.ACCESS_EXPIRES_AT),
      );
    }

    // Refresh if token expires within the next 15 minutes
    if (now > this.tokenExpiresAt - 900) {
      Logger.log(
        '@ getAccessToken() Token is near expiry, refreshing...',
        this.tokenExpiresAt - now,
      );

      await this.refreshAccessToken();
    }

    return this.accessToken;
  }

  async getTokenDetails(): Promise<any> {
    return {
      accessToken: this.accessToken || 'No access token',
      refreshToken: this.refreshToken || 'No refresh token',
      tokenExpiresAt: this.tokenExpiresAt || 'No expiry date',
      tokenExpiresAtDate: new Date(this.tokenExpiresAt * 1000).toISOString(),
    };
  }

  // Uses the refresh token to get a new access token (and update expiry)
  async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      Logger.log('No refresh token available');
      throw new HttpException(
        'No refresh token available',
        HttpStatus.UNAUTHORIZED,
      );
    }

    try {
      const params = new URLSearchParams();
      params.append('grant_type', 'refresh_token');
      params.append('refresh_token', this.refreshToken);
      params.append('client_id', this.clientID);
      params.append('client_secret', this.clientSecret);

      const response = await axios.post(
        `${this.baseUrl}/identity/refresh`,
        params,
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        },
      );

      const data = response.data;

      // Update refresh token if it's different
      if (data.refresh_token && data.refresh_token !== this.refreshToken) {
        Logger.log('Refresh token updated');
        this.refreshToken = data.refresh_token;
      }

      // Update access token and expiry based on the response
      this.accessToken = data.access_token;
      this.tokenExpiresAt = this.parseExpiryDate(data.expireDate);
    } catch (error) {
      Logger.error('Error refreshing access token', error);
      throw new HttpException(
        `Failed to refresh access token. Error: ${error.message}`,
        HttpStatus.UNAUTHORIZED,
      );
    }
  }
}
