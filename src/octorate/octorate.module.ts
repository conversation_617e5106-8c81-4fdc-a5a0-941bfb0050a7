// src/api/api.module.ts
import { Module } from '@nestjs/common';
import { OctorateAuthService } from './octorate-auth.service';
import { OctorateApiService } from './octorate-api.service';
import { OctorateController } from './octorate.controller';
import { CacheModule } from '@nestjs/cache-manager';

@Module({
  imports: [CacheModule.register()],
  providers: [OctorateAuthService, OctorateApiService],
  exports: [OctorateApiService],
  controllers: [OctorateController],
})
export class OctorateModule {}
