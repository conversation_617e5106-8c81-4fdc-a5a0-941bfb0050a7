import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

import { UsersService } from './users.service';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { UserDto, getUserDto } from './dto/user.dto';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from './entities/user.entity';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { CreateUserDto } from 'src/users/dto/create-user.dto';
import { UpdateUserDto } from 'src/users/dto/update-user.dto';
import { fileParser, getDiskStorage, resizeImage } from 'src/common/utils';
import { Vacation } from './entities/vacation.entity';
import { CreateVacationDto } from './dto/create-vacation.dto';
import { ValidateUserDto } from './dto/validate-user.dto';
import { ICalendarEvent } from './user.interface';

const storage = getDiskStorage('./uploads/users');

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('photo', { storage }))
  @Post()
  async create(
    @Body() createUserDto: CreateUserDto,
    @UploadedFile(fileParser) photo: Express.Multer.File,
    @Request() req,
  ) {
    const user = await this.usersService.create(
      createUserDto,
      photo,
      req.user.userId,
    );
    if (photo?.path) {
      await resizeImage(photo);
    }
    return getUserDto(user);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post('validate')
  async validate(
    @Body() validateUserDto: ValidateUserDto,
  ): Promise<{ valid: boolean }> {
    return this.usersService.validate(validateUserDto);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Post(':id/vacations')
  async createUserVacation(
    @Param('id') id: string,
    @Body() createVacationDto: CreateVacationDto,
    @Request() req,
  ) {
    return this.usersService.createUserVacation(
      id,
      createVacationDto,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get()
  async findAll(): Promise<UserDto[]> {
    const users = await this.usersService.findAll();
    return users.map((user) => getUserDto(user));
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('staff')
  async findStaff(@Request() req): Promise<UserDto[]> {
    const users = await this.usersService.findAllStaff(
      req.query?.today,
      req.query?.onlyAvailable === 'true',
      req.query?.onlyUnavailable === 'true',
    );
    return users.map((user) => getUserDto(user));
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('owners')
  async findOwners(): Promise<UserDto[]> {
    const users = await this.usersService.findAllOwners();
    return users.map((user) => getUserDto(user));
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get('my-calendar')
  async getMyCalendar(@Request() req): Promise<ICalendarEvent[]> {
    return this.usersService.getCalendar(req.user.userId, req.query?.month);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':id/calendar')
  async getCalendar(
    @Param('id') id: string,
    @Request() req,
  ): Promise<ICalendarEvent[]> {
    return this.usersService.getCalendar(id, req.query?.month);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':id/hours-calendar')
  async getHoursCalendar(
    @Param('id') id: string,
    @Request() req,
  ): Promise<ICalendarEvent[]> {
    return this.usersService.getHoursCalendar(id, req.query?.month);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get('vacations')
  async findAllVacations(): Promise<Vacation[]> {
    return this.usersService.findAllVacations();
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':id/vacations')
  async findUserVacations(@Param('id') id: string): Promise<Vacation[]> {
    return this.usersService.findUserVacations(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':id')
  async findOne(@Param('id') id: string): Promise<UserDto> {
    const user = await this.usersService.findOneById(id);
    return getUserDto(user);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch('vacations/:id')
  async updateVacation(
    @Param('id') id: string,
    @Body() updateVacationDto: CreateVacationDto,
    @Request() req,
  ): Promise<Vacation> {
    return this.usersService.updateVacation(
      id,
      updateVacationDto,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Patch('update-names')
  async updateNames(): Promise<UserDto[]> {
    const users = await this.usersService.updateNamesFromDisplayName();
    return users.map((user) => getUserDto(user));
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('photo', { storage }))
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @UploadedFile(fileParser) photo: Express.Multer.File,
    @Request() req,
  ): Promise<UserDto> {
    const updatedUser = await this.usersService.update(
      id,
      updateUserDto,
      photo,
      req.user.userId,
    );
    if (photo?.path) {
      await resizeImage(photo);
    }
    return getUserDto(updatedUser);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.usersService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('vacations/:id')
  async removeVacation(
    @Param('id') id: string,
    @Request() req,
  ): Promise<Vacation> {
    return this.usersService.removeVacation(id, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req): Promise<UserDto> {
    const user = await this.usersService.remove(id, req.user.userId);
    return getUserDto(user);
  }
}
