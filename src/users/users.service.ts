import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import {
  Equal,
  In,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import * as bcrypt from 'bcrypt';

import { repositories } from 'src/common/constants';
import { User, UserRole } from './entities/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { Vacation } from './entities/vacation.entity';
import { CreateVacationDto } from './dto/create-vacation.dto';
import { UpdateVacationDto } from './dto/update-vacation.dto';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import { ModuleRef } from '@nestjs/core';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';
import { ValidateUserDto } from './dto/validate-user.dto';
import { ICalendarEvent } from './user.interface';

@Injectable()
export class UsersService implements OnModuleInit {
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.user)
    private userRepository: Repository<User>,
    @Inject(repositories.vacation)
    private vacationRepository: Repository<Vacation>,
  ) {}

  onModuleInit() {
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  private async comparePasswords(
    userPassword: string,
    currentPassword: string,
  ): Promise<boolean> {
    return await bcrypt.compare(currentPassword, userPassword);
  }

  async findAll(): Promise<User[]> {
    const users = await this.userRepository.find();
    return users.map((user) => {
      delete user.password;
      return user;
    });
  }

  async findOneByRole(role: UserRole): Promise<User> {
    return this.userRepository.findOneBy({ role: role });
  }

  async findAllStaff(
    today?: string,
    onlyAvailable?: boolean,
    onlyUnavailable?: boolean,
  ): Promise<User[]> {
    const startDate = today ? new Date(Date.parse(today)) : new Date();
    startDate.setHours(0, 0, 0, 0);

    const endDate = today ? new Date(Date.parse(today)) : new Date();
    endDate.setHours(23, 59, 59, 999);

    // Find staff on vacation
    const staffOnVacation = await this.vacationRepository.find({
      select: { user: { id: true } },
      relations: ['user'],
      withDeleted: false,
      where: [
        {
          start: LessThanOrEqual(startDate),
          end: Equal(endDate),
          user: { deleted: IsNull() },
          deleted: IsNull(),
        },
        {
          start: LessThanOrEqual(startDate),
          end: MoreThanOrEqual(endDate),
          user: { deleted: IsNull() },
          deleted: IsNull(),
        },
        {
          start: Equal(startDate),
          end: MoreThanOrEqual(endDate),
          user: { deleted: IsNull() },
          deleted: IsNull(),
        },
      ],
    });

    const staffOnVacationIds = [
      ...new Set(
        staffOnVacation
          .filter((vacation) => vacation.user)
          .map((vacation) => vacation.user.id),
      ),
    ];

    // Find users with jobs today
    const usersWithJobs = await this.userRepository
      .createQueryBuilder('user')
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.isDriver',
        'user.isActive',
        'user.role',
      ])
      .where('user.role IN (:...roles)', {
        roles: [UserRole.DRIVER, UserRole.USER],
      })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('DISTINCT job.userId')
          .from('job', 'job')
          .where('job.start >= :startDate', { startDate })
          .andWhere('job.end <= :endDate', { endDate })
          .andWhere('job.deleted IS NULL')
          .getQuery();
        return 'user.id IN ' + subQuery;
      })
      .getMany();

    // Find users with helper jobs today
    const usersWithHelperJobs = await this.userRepository
      .createQueryBuilder('user')
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.isDriver',
        'user.isActive',
        'user.role',
      ])
      .where('user.role IN (:...roles)', {
        roles: [UserRole.DRIVER, UserRole.USER],
      })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('DISTINCT job_helpers_user.userId')
          .from('job_helpers_user', 'job_helpers_user')
          .innerJoin('job', 'job', 'job.id = job_helpers_user.jobId')
          .where('job.start >= :startDate', { startDate })
          .andWhere('job.end <= :endDate', { endDate })
          .andWhere('job.deleted IS NULL')
          .getQuery();
        return 'user.id IN ' + subQuery;
      })
      .getMany();

    // Find active staff
    const activeStaff = await this.userRepository.find({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        isDriver: true,
        isActive: true,
        isVerifier: true,
        role: true,
      },
      where: {
        role: In([UserRole.DRIVER, UserRole.USER]),
        isActive: true,
      },
    });

    // Merge all users and remove duplicates
    const allUsers = [...usersWithJobs, ...usersWithHelperJobs, ...activeStaff];
    const uniqueUsers = Array.from(
      new Map(allUsers.map((user) => [user.id, user])).values(),
    );

    // Apply vacation filtering if needed
    let filteredUsers = uniqueUsers;
    if (onlyAvailable && !onlyUnavailable) {
      filteredUsers = uniqueUsers.filter(
        (user) => !staffOnVacationIds.includes(user.id),
      );
    } else if (onlyUnavailable && !onlyAvailable) {
      filteredUsers = uniqueUsers.filter((user) =>
        staffOnVacationIds.includes(user.id),
      );
    }

    // Sort results
    return filteredUsers.sort((a, b) => {
      if (a.isDriver !== b.isDriver) {
        return b.isDriver ? 1 : -1;
      }
      return a.firstName.localeCompare(b.firstName);
    });
  }

  async findAllOwners(): Promise<User[]> {
    const owners = await this.userRepository.find({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        isActive: true,
        role: true,
      },
      where: {
        role: UserRole.OWNER,
        isActive: true,
      },
      order: { firstName: 'ASC' },
    });

    return owners;
  }

  async findAllVacations(): Promise<Vacation[]> {
    return this.vacationRepository.find();
  }

  async findUserVacations(userId: string): Promise<Vacation[]> {
    return this.vacationRepository.find({
      where: {
        user: { id: userId },
      },
      relations: ['user'],
    });
  }

  async findAllByRole(role: UserRole): Promise<User[]> {
    return this.userRepository.findBy({ role });
  }

  async findAllByIds(ids: string[]): Promise<User[]> {
    return this.userRepository.findBy({ id: In(ids) });
  }

  async findOneById(id: string): Promise<User | undefined> {
    return this.userRepository.findOne({
      where: { id },
      relations: ['places'],
    });
  }

  async findOneByUsername(username: string): Promise<User | undefined> {
    // Force lowercase username
    username = username.toLowerCase();
    return this.userRepository.findOne({
      where: { username },
      relations: ['places'],
    });
  }

  async findOneByEmail(email: string): Promise<User | undefined> {
    return this.userRepository.findOne({
      where: { email },
      relations: ['places'],
    });
  }

  async validate(
    validateUserDto: ValidateUserDto,
  ): Promise<{ valid: boolean }> {
    // Check username length if provided
    if (validateUserDto.username && validateUserDto.username.length < 2) {
      return { valid: false };
    }

    const existingUser = await this.userRepository.findOne({
      where: [
        { username: validateUserDto.username?.toLowerCase() },
        { email: validateUserDto.email?.toLowerCase() },
      ],
    });

    return { valid: !existingUser };
  }

  async getCalendar(
    userId: string,
    yearMonth?: string,
  ): Promise<ICalendarEvent[]> {
    const [yearParam, monthParam] = yearMonth?.split('-') || [];
    const month = monthParam
      ? parseInt(monthParam, 10) - 1
      : new Date().getMonth();
    const year = yearParam ? parseInt(yearParam, 10) : new Date().getFullYear();

    // Set start and end of the month in local time
    const startOfMonth = new Date(year, month, 1);
    startOfMonth.setHours(0, 0, 0, 0);
    const endOfMonth = new Date(year, month + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);

    // Helper function to get local date string (YYYY-MM-DD)
    const getLocalDateStr = (date: Date): string => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    // Get jobs for the user within the month
    const jobs = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.jobs', 'job')
      .where('user.id = :userId', { userId })
      .andWhere('job.start >= :startOfMonth', { startOfMonth })
      .andWhere('job.end <= :endOfMonth', { endOfMonth })
      .andWhere('job.deleted IS NULL')
      .getMany();

    // Get vacations overlapping the month
    const vacations = await this.vacationRepository
      .createQueryBuilder('vacation')
      .leftJoinAndSelect('vacation.user', 'user')
      .where('user.id = :userId', { userId })
      .andWhere('vacation.start <= :endOfMonth', { endOfMonth })
      .andWhere('vacation.end >= :startOfMonth', { startOfMonth })
      .andWhere('vacation.deleted IS NULL')
      .getMany();

    const datesWithEvents = new Set<string>();
    const calendarEvents: ICalendarEvent[] = [];

    // Track unique workday dates
    const workdayDates = new Set<string>();

    // Process jobs and collect unique workday dates
    jobs.forEach((user) => {
      user.jobs.forEach((job) => {
        const jobDate = new Date(job.start);
        const localDateStr = getLocalDateStr(jobDate);
        datesWithEvents.add(localDateStr);
        workdayDates.add(localDateStr); // Collect unique workday dates
      });
    });

    // Add single workday event for each unique date
    workdayDates.forEach((localDateStr) => {
      calendarEvents.push({
        id: `workday-${localDateStr}`,
        type: 'workday',
        start: new Date(`${localDateStr}T00:00:00.000Z`), // Consistent ISO format
        allDay: true,
        textColor: 'green',
        display: 'background',
        className: 'bg-event',
        backgroundColor: 'green',
      });
    });

    // Process vacations
    vacations.forEach((vacation) => {
      const vacationStart = new Date(vacation.start);
      vacationStart.setHours(0, 0, 0, 0);
      const vacationEnd = new Date(vacation.end);
      vacationEnd.setHours(23, 59, 59, 999);

      // Add each day of the vacation to datesWithEvents
      const vacationDate = new Date(vacationStart);
      while (vacationDate <= vacationEnd) {
        const localDateStr = getLocalDateStr(vacationDate);
        datesWithEvents.add(localDateStr);
        vacationDate.setDate(vacationDate.getDate() + 1);
      }

      // Calculate the exclusive end date for FullCalendar
      const endDate = new Date(vacationEnd);
      endDate.setDate(endDate.getDate() + 1);
      const vacationEndExclusive = getLocalDateStr(endDate);

      calendarEvents.push({
        id: vacation.id,
        type: 'vacation',
        start: new Date(`${getLocalDateStr(vacationStart)}T00:00:00.000Z`),
        end: new Date(`${vacationEndExclusive}T00:00:00.000Z`),
        allDay: true,
        textColor: 'red',
        display: 'background',
        className: 'bg-event',
        backgroundColor: 'red',
      });
    });

    // Add offdays for all days not in datesWithEvents
    const currentDate = new Date(startOfMonth);
    while (currentDate <= endOfMonth) {
      const localDateStr = getLocalDateStr(currentDate);
      if (!datesWithEvents.has(localDateStr)) {
        calendarEvents.push({
          id: `off-${localDateStr}`,
          type: 'offday',
          start: new Date(`${localDateStr}T00:00:00.000Z`),
          allDay: true,
          textColor: 'orange',
          display: 'background',
          className: 'bg-event',
          backgroundColor: 'orange',
        });
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Sort events by start date
    calendarEvents.sort(
      (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime(),
    );
    return calendarEvents;
  }

  async getHoursCalendar(
    userId: string,
    yearMonth?: string,
  ): Promise<ICalendarEvent[]> {
    const [yearParam, monthParam] = yearMonth?.split('-') || [];
    const month = monthParam
      ? parseInt(monthParam, 10) - 1
      : new Date().getMonth();
    const year = yearParam ? parseInt(yearParam, 10) : new Date().getFullYear();

    // Set start and end of the month in local time
    const startOfMonth = new Date(year, month, 1);
    startOfMonth.setHours(0, 0, 0, 0);
    const endOfMonth = new Date(year, month + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);

    // Helper function to get local date string (YYYY-MM-DD)
    const getLocalDateStr = (date: Date): string => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    // Get jobs for the user within the month
    const jobs = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.jobs', 'job')
      .where('user.id = :userId', { userId })
      .andWhere('job.start >= :startOfMonth', { startOfMonth })
      .andWhere('job.end <= :endOfMonth', { endOfMonth })
      .andWhere('job.deleted IS NULL')
      .getMany();

    const calendarEvents: ICalendarEvent[] = [];

    // Process jobs and collect unique workday dates
    jobs.forEach((user) => {
      user.jobs.forEach((job) => {
        const jobDate = new Date(job.start);
        const localDateStr = getLocalDateStr(jobDate);
        calendarEvents.push({
          id: job.id,
          type: 'workday',
          start: new Date(`${localDateStr}T00:00:00.000Z`), // Consistent ISO format
          allDay: true,
          textColor: 'green',
          display: 'background',
          className: 'bg-event',
          backgroundColor: 'green',
        });
      });
    });

    // Sort events by
  }

  async create(
    userDto: CreateUserDto,
    photo?: Express.Multer.File,
    authUserId?: string,
  ): Promise<User> {
    // Force lowercase username
    userDto.username = userDto.username.toLowerCase();

    const userExists = await this.findOneByUsername(userDto.username);
    if (userExists) {
      throw new HttpException(
        'User with this username already exists',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Hash user password
    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash(userDto.password, salt);
    userDto.password = hashedPassword;

    const newUser = this.userRepository.create({
      ...userDto,
      photo: photo ? '/' + photo.path : null,
      isDriver: userDto.isDriver === 'true',
      isVerifier: userDto.isVerifier === 'true',
      isActive: true,
    });

    const savedUser = await this.userRepository.save(newUser);

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.CREATE,
      entityType: AuditlogEntityType.USER,
      entityId: savedUser.id,
      userId: authUserId,
      before: null,
      after: savedUser,
    });

    return savedUser;
  }

  async createUserVacation(
    id: string,
    createVacationDto: CreateVacationDto,
    authUserId?: string,
  ): Promise<Vacation> {
    const user = await this.userRepository.findOne({
      select: ['id'],
      where: { id },
    });

    if (!user) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }

    const vacation = this.vacationRepository.create({
      ...createVacationDto,
      user,
    });

    const savedVacation = await this.vacationRepository.save(vacation);

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.CREATE,
      entityType: AuditlogEntityType.VACATION,
      entityId: savedVacation.id,
      userId: authUserId,
      before: null,
      after: savedVacation,
    });

    return savedVacation;
  }

  async updateVacation(
    id: string,
    updateVacationDto: UpdateVacationDto,
    authUserId?: string,
  ): Promise<Vacation> {
    const vacation = await this.vacationRepository.findOneBy({ id });

    if (!vacation) {
      throw new HttpException('Vacation not found', HttpStatus.BAD_REQUEST);
    }

    const before = JSON.stringify(vacation);

    const savedVacation = await this.vacationRepository.save(
      this.vacationRepository.merge(vacation, updateVacationDto),
    );

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.UPDATE,
      entityType: AuditlogEntityType.VACATION,
      entityId: savedVacation.id,
      userId: authUserId,
      before: JSON.parse(before),
      after: savedVacation,
    });

    return savedVacation;
  }

  async update(
    id: string,
    updateUserDto: UpdateUserDto,
    photo: Express.Multer.File,
    authUserId?: string,
  ): Promise<User> {
    const user = await this.findOneById(id);

    if (!user) {
      throw new HttpException(
        'User with this id does not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    const before = JSON.stringify(user);

    if (updateUserDto.password) {
      // Hash user password
      const salt = await bcrypt.genSalt();
      const hashedPassword = await bcrypt.hash(updateUserDto.password, salt);
      updateUserDto.password = hashedPassword;
    } else {
      delete updateUserDto.password;
    }

    const updatedUser = this.userRepository.merge(user, {
      ...updateUserDto,
      photo: photo ? '/' + photo.path : user.photo,
      isDriver: updateUserDto.isDriver === 'true',
      isVerifier: updateUserDto.isVerifier === 'true',
      ...(updateUserDto.isActive && {
        isActive: updateUserDto.isActive === 'true',
      }),
    });

    const savedUser = await this.userRepository.save(updatedUser);

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.UPDATE,
      entityType: AuditlogEntityType.USER,
      entityId: savedUser.id,
      userId: authUserId,
      before: JSON.parse(before),
      after: savedUser,
    });

    return savedUser;
  }

  async remove(id: string, authUserId: string): Promise<User> {
    const user = await this.findOneById(id);
    if (!user) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.USER,
      entityId: id,
      userId: authUserId,
      before: user,
      after: null,
    });

    return this.userRepository.softRemove(user);
  }

  async removeVacation(id: string, authUserId: string): Promise<Vacation> {
    const vacation = await this.vacationRepository.findOneBy({ id });
    if (!vacation) {
      throw new HttpException('Vacation not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.VACATION,
      entityId: id,
      userId: authUserId,
      before: vacation,
      after: null,
    });

    return this.vacationRepository.softRemove(vacation);
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const users = await this.userRepository.findBy({ id: In(ids) });
    users.forEach(async (user) => {
      await this.userRepository.softRemove(user);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.USER,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: users.map((user) => user.id),
      after: null,
    });

    return true;
  }

  async revokeRefreshToken(id: string): Promise<User> {
    const user = await this.findOneById(id);
    if (!user) {
      throw new HttpException(
        'User with this id does not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    user.refreshToken = null;
    return this.userRepository.save(user);
  }

  async updateRefreshToken(id: string, refreshToken: string): Promise<User> {
    const user = await this.findOneById(id);
    if (!user) {
      throw new HttpException(
        'User with this id does not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    user.refreshToken = refreshToken;
    return this.userRepository.save(user);
  }

  async validateCredentials(
    username: string,
    password: string,
  ): Promise<User | undefined> {
    const user = await this.findOneByUsername(username);

    if (user && (await this.comparePasswords(user.password, password))) {
      return user;
    }

    return undefined;
  }

  async updatePassword(userId: string, password: string): Promise<User> {
    const user = await this.findOneById(userId);

    if (!user) {
      throw new HttpException(
        'User with this id does not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash(password, salt);

    user.password = hashedPassword;
    return this.userRepository.save(user);
  }

  async updateNamesFromDisplayName(): Promise<User[]> {
    const users = await this.userRepository.find({
      where: { role: UserRole.USER },
    });
    users.forEach((user) => {
      const displayName = user.displayName.split(' ');
      user.firstName = displayName[0];
      user.lastName = displayName[1];
      user.username = displayName[0].toLowerCase();
      user.email =
        displayName[0] + '.' + displayName[1] + '@' + user.email.split('@')[1];
    });

    return this.userRepository.save(users);
  }
}
