import { UserRole } from './entities/user.entity';

export interface IUser {
  id?: string;
  username?: string;
  password?: string;
  email?: string;
  displayName?: string;
  photo?: string;
  role?: UserRole;
  refreshToken?: string;
  isActive?: boolean;
  created?: Date;
  updated?: Date;
  deleted?: Date;
}

export interface ICalendarEvent {
  id: string;
  type: 'workday' | 'offday' | 'vacation';
  title?: string;
  start: Date;
  end?: Date;
  display?: 'block' | 'background';
  className?: string;
  allDay?: boolean;
  textColor?: string;
  backgroundColor?: string;
}
