import { DataSource } from 'typeorm';

import { dataSources, repositories } from 'src/common/constants';
import { User } from './entities/user.entity';
import { Vacation } from './entities/vacation.entity';

export const userProviders = [
  {
    provide: repositories.user,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(User),
    inject: [dataSources.default],
  },
  {
    provide: repositories.vacation,
    useFactory: (dataSource: DataSource) => dataSource.getRepository(Vacation),
    inject: [dataSources.default],
  },
];
