import { Place } from 'src/places/entities/place.entity';
import { User, UserRole } from '../entities/user.entity';
import { IUser } from '../user.interface';

export class UserDto implements IUser {
  public readonly id: string;
  public readonly username: string;
  public readonly email: string;
  public readonly phone: string;
  public readonly firstName: string;
  public readonly lastName: string;
  public readonly displayName: string;
  public readonly name: string;
  public readonly photo: string;
  public readonly places: Place[];
  public readonly role: UserRole;
  public readonly isDriver: boolean;
  public readonly isVerifier: boolean;
  public readonly isActive: boolean;
  public readonly status: string;
  public readonly created: Date;
  public readonly updated: Date;
  public readonly deleted: Date;

  constructor(user: User) {
    if (!user || !user.id) {
      return;
    }

    this.id = user.id;
    this.username = user.username;
    this.email = user.email;
    this.phone = user.phone;
    this.firstName = user.firstName;
    this.lastName = user.lastName;
    this.name = `${user.firstName} ${user.lastName}`;
    this.displayName = user.displayName || this.name; // TODO: Drop displayName
    this.photo = user.photo;
    this.places = user.places || [];
    this.role = user.role;
    this.isDriver = user.isDriver;
    this.isActive = user.isActive;
    this.isVerifier = user.isVerifier;
    this.status = user.deleted || !user.isActive ? 'inactive' : 'active';
    this.created = user.created;
    this.updated = user.updated;
    this.deleted = user.deleted;
  }
}

export function getUserDto(user: User): UserDto {
  return new UserDto(user);
}
