import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IUser } from '../user.interface';
import { Job } from 'src/jobs/entities/job.entity';
import { Timelog } from 'src/timelogs/entities/timelog.entity';
import { Booking } from 'src/vehicles/entities/booking.entity';
import { Vacation } from './vacation.entity';
import { Auditlog } from 'src/auditlogs/entities/auditlog.entity';
import { Exclude, Expose } from 'class-transformer';
import { Place } from 'src/places/entities/place.entity';

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  DRIVER = 'driver',
  USER = 'user',
  OWNER = 'owner',
}

@Entity()
export class User implements IUser {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  username: string;

  @Exclude()
  @Column()
  password: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  displayName: string;

  @Column({ nullable: true })
  photo: string;

  @Column({ type: 'enum', enum: UserRole, default: UserRole.USER })
  role: UserRole;

  @OneToMany(() => Job, (job) => job.user)
  jobs: Job[];

  @ManyToMany(() => Job, (job) => job.helpers)
  helperJobs: Job[];

  @OneToMany(() => Timelog, (timelog) => timelog.user)
  timelogs: Timelog[];

  @ManyToMany(() => Booking, (booking) => booking.passengers)
  bookings: Booking[];

  @OneToMany(() => Booking, (booking) => booking.driver)
  drivings: Booking[];

  @OneToMany(() => Vacation, (vacation) => vacation.user)
  vacations: Vacation[];

  @OneToMany(() => Auditlog, (auditlog) => auditlog.user)
  auditlogs: Auditlog[];

  @OneToMany(() => Place, (place) => place.owner)
  places: Place[];

  @Column({ nullable: true })
  refreshToken: string;

  @Column({ default: false })
  isDriver: boolean;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;

  @Column({ default: false })
  isVerifier: boolean;

  @OneToMany(() => Job, (job) => job.cleaningVerifiedBy)
  cleaningVerifications: Job[];

  @Expose()
  get fullName() {
    return `${this.firstName} ${this.lastName}`;
  }
}
