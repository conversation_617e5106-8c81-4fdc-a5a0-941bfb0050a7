import { Module } from '@nestjs/common';
import { TagsService } from './tags.service';
import { TagsController } from './tags.controller';
import { DatabaseModule } from 'src/database/database.module';
import { tagProviders } from './tag.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...tagProviders, TagsService],
  controllers: [TagsController],
  exports: [TagsService],
})
export class TagsModule {}
