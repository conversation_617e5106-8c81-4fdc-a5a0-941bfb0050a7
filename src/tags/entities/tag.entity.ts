import { Asset } from 'src/assets/entities/asset.entity';
import { Bag } from 'src/bags/entities/bag.entity';
import { Place } from 'src/places/entities/place.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Index(['serial'], { unique: true })
export class Tag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  serial: string;

  @OneToOne(() => Place, (place) => place.tag, { nullable: true })
  place: Place;

  @OneToOne(() => Bag, (bag) => bag.tag, { nullable: true })
  bag: Bag;

  @OneToOne(() => Asset, (asset) => asset.tag, { nullable: true })
  asset: Asset;

  @CreateDateColumn()
  created: Date;

  @UpdateDateColumn()
  updated: Date;

  @DeleteDateColumn()
  deleted: Date;

  public get isLinked(): boolean {
    return this.place !== null || this.bag !== null || this.asset !== null;
  }
}
