import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { In, Raw, Repository } from 'typeorm';
import { CreateTagDto } from './dto/create-tag.dto';
import { repositories } from 'src/common/constants';
import { Tag } from './entities/tag.entity';
import { UpdateTagDto } from './dto/update-tag.dto';
import { PlacesService } from 'src/places/places.service';
import { ModuleRef } from '@nestjs/core';
import { Place } from 'src/places/entities/place.entity';
import { Bag } from 'src/bags/entities/bag.entity';
import { BagsService } from 'src/bags/bags.service';
import { Asset } from 'src/assets/entities/asset.entity';
import { AssetsService } from 'src/assets/assets.service';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';

@Injectable()
export class TagsService implements OnModuleInit {
  private placesService: PlacesService;
  private bagsService: BagsService;
  private assetsService: AssetsService;
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.tag)
    private tagRepository: Repository<Tag>,
  ) {}

  onModuleInit() {
    this.placesService = this.moduleRef.get(PlacesService, { strict: false });
    this.bagsService = this.moduleRef.get(BagsService, { strict: false });
    this.assetsService = this.moduleRef.get(AssetsService, { strict: false });
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async create(createTagDto: CreateTagDto, authUserId?: string) {
    // Create tag
    try {
      let place: Place = null;
      if (createTagDto.place) {
        place = await this.placesService.findOneById(createTagDto.place);

        if (!place) {
          throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
        }
      }

      let bag: Bag = null;
      if (createTagDto.bag) {
        bag = await this.bagsService.findOne(createTagDto.bag);

        if (!bag) {
          throw new HttpException('Bag not found', HttpStatus.BAD_REQUEST);
        }
      }

      let asset: Asset = null;
      if (createTagDto.asset) {
        asset = await this.assetsService.findOne(createTagDto.asset);

        if (!asset) {
          throw new HttpException('Asset not found', HttpStatus.BAD_REQUEST);
        }
      }

      const tag = this.tagRepository.create({
        serial: createTagDto.serial,
        place,
        bag,
        asset,
      });

      const createdTag = await this.tagRepository.save(tag);

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.CREATE,
          entityType: AuditlogEntityType.TAG,
          entityId: createdTag.id,
          userId: authUserId,
          before: null,
          after: createdTag,
        });
      }

      return this.findOne(createdTag.id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  findAll() {
    return this.tagRepository.find({
      select: {
        id: true,
        serial: true,
        created: true,
        updated: true,
        place: {
          id: true,
          title: true,
          area: {
            id: true,
            name: true,
            parent: { id: true, name: true },
          },
        },
        bag: {
          id: true,
          place: {
            id: true,
            title: true,
            area: {
              id: true,
              name: true,
              parent: { id: true, name: true },
            },
          },
        },
        asset: {
          id: true,
          name: true,
          place: {
            id: true,
            title: true,
            area: {
              id: true,
              name: true,
              parent: { id: true, name: true },
            },
          },
        },
      },
      relations: [
        'place.area.parent',
        'bag.place.area.parent',
        'asset.place.area.parent',
      ],
    });
  }

  findOne(id: string) {
    return this.tagRepository.findOne({
      select: {
        id: true,
        serial: true,
        created: true,
        updated: true,
        place: {
          id: true,
          title: true,
          image: true,
          area: {
            id: true,
            name: true,
            parent: { id: true, name: true },
          },
        },
        bag: {
          id: true,
          place: {
            id: true,
            title: true,
            area: {
              id: true,
              name: true,
              parent: { id: true, name: true },
            },
          },
        },
        asset: {
          id: true,
          name: true,
          place: {
            id: true,
            title: true,
            area: {
              id: true,
              name: true,
              parent: { id: true, name: true },
            },
          },
        },
      },
      where: { id },
      relations: [
        'place.area.parent',
        'bag.place.area.parent',
        'asset.place.area.parent',
      ],
    });
  }

  findSerial(serial: string) {
    return this.tagRepository.findOne({
      where: {
        serial: Raw((alias) => `LOWER(${alias}) = '${serial.toLowerCase()}'`),
      },
      relations: [
        'place.area.parent',
        'bag.place.area.parent',
        'asset.place.area.parent',
      ],
    });
  }

  async update(id: string, updateTagDto: UpdateTagDto, authUserId?: string) {
    const tag = await this.tagRepository.findOneBy({ id });

    if (!tag) {
      throw new HttpException('Tag not found', HttpStatus.BAD_REQUEST);
    }

    const before = JSON.stringify(tag);

    let place: Place = null;

    if (updateTagDto.place) {
      place = await this.placesService.findOneById(updateTagDto.place);

      if (!place) {
        throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
      }
    }

    let bag: Bag = null;
    if (updateTagDto.bag) {
      bag = await this.bagsService.findOne(updateTagDto.bag);

      if (!bag) {
        throw new HttpException('Bag not found', HttpStatus.BAD_REQUEST);
      }
    }

    let asset: Asset = null;
    if (updateTagDto.asset) {
      asset = await this.assetsService.findOne(updateTagDto.asset);

      if (!asset) {
        throw new HttpException('Asset not found', HttpStatus.BAD_REQUEST);
      }
    }

    // Update tag
    const savedTag = await this.tagRepository.save({
      ...tag,
      ...updateTagDto,
      place,
      bag,
      asset,
    });

    // Log activity
    if (authUserId) {
      await this.auditlogService.create({
        action: AuditlogAction.UPDATE,
        entityType: AuditlogEntityType.TAG,
        entityId: id,
        userId: authUserId,
        before: JSON.parse(before),
        after: savedTag,
      });
    }

    return savedTag;
  }

  async remove(id: string, authUserId: string) {
    const tag = await this.tagRepository.findOneBy({ id });

    if (!tag) {
      throw new HttpException('Tag not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.TAG,
      entityId: id,
      userId: authUserId,
      before: tag,
      after: null,
    });

    return this.tagRepository.softRemove(tag);
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const tags = await this.tagRepository.findBy({ id: In(ids) });
    tags.forEach(async (tag) => {
      await this.tagRepository.softRemove(tag);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.TAG,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: tags.map((tag) => tag.id),
      after: null,
    });

    return true;
  }
}
