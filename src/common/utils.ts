import {
  FileTypeValidator,
  MaxFileSizeValidator,
  ParseFilePipe,
} from '@nestjs/common';
import { diskStorage } from 'multer';
import { extname, join, parse } from 'path';
import * as sharp from 'sharp';

export const getDiskStorage = (destination: string) => {
  return diskStorage({
    destination,
    filename: (req, file, cb) => {
      const name = file.originalname.split('.')[0];
      const extension = extname(file.originalname);
      const randomName = Array(32)
        .fill(null)
        .map(() => Math.round(Math.random() * 16).toString(16))
        .join('');
      cb(null, `${name}-${randomName}${extension}`);
    },
  });
};

export const fileParser = new ParseFilePipe({
  validators: [
    new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }),
    new FileTypeValidator({ fileType: 'image/jpeg' }),
  ],
  fileIsRequired: false,
});

export const resizeImage = async (image: Express.Multer.File) => {
  try {
    // Use sharp to resize the image to 2 sizes
    const filePath = image.destination;
    const fileName = parse(image.filename).name;
    const fileExtension = parse(image.filename).ext;

    await sharp(image.path)
      .resize({
        width: 250,
        height: 250,
        fit: 'inside',
        withoutEnlargement: true,
      })
      .jpeg({ quality: 80 })
      .toFile(join(filePath, `${fileName}-small${fileExtension}`));

    // Use sharp to resize the image to 1200px wide for full screen display
    await sharp(image.path)
      .resize({
        width: 2000,
        height: 2000,
        fit: 'inside',
        withoutEnlargement: true,
      })
      .jpeg({ quality: 80 })
      .toFile(join(filePath, `${fileName}-large${fileExtension}`));
  } catch (error) {
    throw new Error('Image processing failed');
  }
};
