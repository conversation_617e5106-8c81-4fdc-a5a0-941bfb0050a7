export const jwtConstants = {
  accessStrategy: 'jwt-access',
  accessSecret: `${process.env.JWT_ACCESS_SECRET}`,
  refreshStrategy: 'jwt-refresh',
  refreshSecret: `${process.env.JWT_REFRESH_SECRET}`,
};

export const dataSources = {
  default: 'DATA_SOURCE',
};

export const repositories = {
  user: 'USER_REPOSITORY',
  place: 'PLACE_REPOSITORY',
  job: 'JOB_REPOSITORY',
  timelog: 'TIMELOG_REPOSITORY',
  tag: 'TAG_REPOSITORY',
  area: 'AREA_REPOSITORY',
  bag: 'BAG_REPOSITORY',
  typology: 'TYPOLOGY_REPOSITORY',
  vehicle: 'VEHICLE_REPOSITORY',
  booking: 'BOOKING_REPOSITORY',
  asset: 'ASSET_REPOSITORY',
  vacation: 'VACATION_REPOSITORY',
  incident: 'INCIDENT_REPOSITORY',
  auditlog: 'AUDITLOG_REPOSITORY',
  assetRequest: 'ASSET_REQUEST_REPOSITORY',
};
