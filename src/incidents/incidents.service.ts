import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { CreateIncidentDto } from './dto/create-incident.dto';
import { UpdateIncidentDto } from './dto/update-incident.dto';
import { ModuleRef } from '@nestjs/core';
import { repositories } from 'src/common/constants';
import { In, Repository } from 'typeorm';
import { Incident } from './entities/incident.entity';
import { PlacesService } from 'src/places/places.service';
import { AssetsService } from 'src/assets/assets.service';
import { AuditlogsService } from 'src/auditlogs/auditlogs.service';
import {
  AuditlogAction,
  AuditlogEntityType,
} from 'src/auditlogs/entities/auditlog.entity';

@Injectable()
export class IncidentsService implements OnModuleInit {
  private placesService: PlacesService;
  private assetsService: AssetsService;
  private auditlogService: AuditlogsService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.incident)
    private incidentRepository: Repository<Incident>,
  ) {}

  onModuleInit() {
    this.placesService = this.moduleRef.get(PlacesService, { strict: false });
    this.assetsService = this.moduleRef.get(AssetsService, { strict: false });
    this.auditlogService = this.moduleRef.get(AuditlogsService, {
      strict: false,
    });
  }

  async create(
    createIncidentDto: CreateIncidentDto,
    image?: Express.Multer.File,
    authUserId?: string,
  ) {
    try {
      let place = null;
      let asset = null;

      if (createIncidentDto.place) {
        place = await this.placesService.findOne(createIncidentDto.place);

        if (!place) {
          throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
        }
      }

      if (createIncidentDto.asset) {
        asset = await this.assetsService.findOne(createIncidentDto.asset);

        if (!asset) {
          throw new HttpException('Asset not found', HttpStatus.BAD_REQUEST);
        }
      }

      const incident = this.incidentRepository.create({
        ...createIncidentDto,
        place,
        asset,
        image: image ? '/' + image.path : null,
      });

      const savedIncident = await this.incidentRepository.save(incident);

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.CREATE,
          entityType: AuditlogEntityType.INCIDENT,
          entityId: savedIncident.id,
          userId: authUserId,
          before: null,
          after: savedIncident,
        });
      }

      return savedIncident;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  findAll() {
    return this.incidentRepository.find({
      relations: ['place.area.parent', 'asset'],
    });
  }

  findOne(id: string) {
    return this.incidentRepository.findOne({
      where: { id },
      relations: ['place.area.parent', 'asset'],
    });
  }

  async update(
    id: string,
    updateIncidentDto: UpdateIncidentDto,
    image?: Express.Multer.File,
    authUserId?: string,
  ) {
    try {
      const incident = await this.incidentRepository.findOne({
        where: { id },
        relations: ['place', 'asset'],
      });

      if (!incident) {
        throw new HttpException('Incident not found', HttpStatus.BAD_REQUEST);
      }

      const before = JSON.stringify(incident);

      let place = null;
      let asset = null;

      if (updateIncidentDto.place) {
        place = await this.placesService.findOne(updateIncidentDto.place);

        if (!place) {
          throw new HttpException('Place not found', HttpStatus.BAD_REQUEST);
        }
      }

      if (updateIncidentDto.asset) {
        asset = await this.assetsService.findOne(updateIncidentDto.asset);

        if (!asset) {
          throw new HttpException('Asset not found', HttpStatus.BAD_REQUEST);
        }
      }

      const updatedIncident = this.incidentRepository.merge(incident, {
        ...updateIncidentDto,
        place,
        asset,
        image: image ? '/' + image.path : incident.image,
      });

      const savedIncident = await this.incidentRepository.save(updatedIncident);

      // Log activity
      if (authUserId) {
        await this.auditlogService.create({
          action: AuditlogAction.UPDATE,
          entityType: AuditlogEntityType.INCIDENT,
          entityId: id,
          userId: authUserId,
          before: JSON.parse(before),
          after: savedIncident,
        });
      }

      return savedIncident;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  async remove(id: string, authUserId: string) {
    const incident = this.incidentRepository.findOneBy({ id });
    if (!incident) {
      throw new HttpException('Incident not found', HttpStatus.BAD_REQUEST);
    }

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.INCIDENT,
      entityId: id,
      userId: authUserId,
      before: incident,
      after: null,
    });

    return this.incidentRepository.softRemove({ id });
  }

  async batchRemove(ids: string[], authUserId: string): Promise<boolean> {
    const incidents = await this.incidentRepository.findBy({ id: In(ids) });
    incidents.forEach(async (incident) => {
      await this.incidentRepository.softRemove(incident);
    });

    // Log activity
    await this.auditlogService.create({
      action: AuditlogAction.DELETE,
      entityType: AuditlogEntityType.INCIDENT,
      entityId: ids.length === 1 ? ids.toString() : 'Multiple',
      userId: authUserId,
      before: incidents.map((incident) => incident.id),
      after: null,
    });

    return true;
  }
}
