import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
} from '@nestjs/common';
import { IncidentsService } from './incidents.service';
import { CreateIncidentDto } from './dto/create-incident.dto';
import { UpdateIncidentDto } from './dto/update-incident.dto';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { UserRole } from 'src/users/entities/user.entity';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { fileParser, getDiskStorage, resizeImage } from 'src/common/utils';

const storage = getDiskStorage('./uploads/incidents');

@Controller('incidents')
export class IncidentsController {
  constructor(private readonly incidentsService: IncidentsService) {}

  @UseGuards(JwtAccessAuthGuard)
  @UseInterceptors(FileInterceptor('image', { storage }))
  @Post()
  async create(
    @Body() createIncidentDto: CreateIncidentDto,
    @UploadedFile(fileParser) image: Express.Multer.File,
    @Request() req,
  ) {
    if (image?.path) {
      await resizeImage(image);
    }
    return this.incidentsService.create(
      createIncidentDto,
      image,
      req.user.userId,
    );
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get()
  findAll() {
    return this.incidentsService.findAll();
  }

  @UseGuards(JwtAccessAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.incidentsService.findOne(id);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @UseInterceptors(FileInterceptor('image', { storage }))
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateIncidentDto: UpdateIncidentDto,
    @UploadedFile(fileParser) image: Express.Multer.File,
    @Request() req,
  ) {
    if (image?.path) {
      await resizeImage(image);
    }
    return this.incidentsService.update(
      id,
      updateIncidentDto,
      image,
      req.user.userId,
    );
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete('batch')
  async batchRemove(
    @Body('ids') ids: string[],
    @Request() req,
  ): Promise<boolean> {
    return await this.incidentsService.batchRemove(ids, req.user.userId);
  }

  @HasRoles(UserRole.ADMIN)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.incidentsService.remove(id, req.user.userId);
  }
}
