import { Module } from '@nestjs/common';
import { IncidentsService } from './incidents.service';
import { IncidentsController } from './incidents.controller';
import { DatabaseModule } from 'src/database/database.module';
import { incidentProviders } from './incident.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...incidentProviders, IncidentsService],
  controllers: [IncidentsController],
  exports: [IncidentsService],
})
export class IncidentsModule {}
