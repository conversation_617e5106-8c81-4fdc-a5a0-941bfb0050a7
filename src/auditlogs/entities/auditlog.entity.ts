import { User } from 'src/users/entities/user.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

export enum AuditlogAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  IMPORT = 'import',
}

export enum AuditlogEntityType {
  USER = 'user',
  JOB = 'job',
  VACATION = 'vacation',
  ASSET = 'asset',
  ASSET_REQUEST = 'asset_request',
  AREA = 'area',
  PLACE = 'place',
  TYPOLOGY = 'typology',
  BAG = 'bag',
  VEHICLE = 'vehicle',
  INCIDENT = 'incident',
  TAG = 'tag',
  BOOKING = 'booking',
}

@Entity()
export class Auditlog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: AuditlogAction,
    default: AuditlogAction.CREATE,
  })
  action: AuditlogAction;

  @Column({
    type: 'enum',
    enum: AuditlogEntityType,
    default: AuditlogEntityType.JOB,
  })
  entityType: AuditlogEntityType;

  @Column({ nullable: true })
  entityId: string;

  @ManyToOne(() => User, (user) => user.auditlogs)
  user: User;

  @Column('jsonb', { nullable: true })
  diff: Record<string, any>;

  @Column('jsonb', { nullable: true })
  before: Record<string, any>;

  @Column('jsonb', { nullable: true })
  after: Record<string, any>;

  @CreateDateColumn()
  created: Date;
}

export class AuditlogWithEntity extends Auditlog {
  entity: Record<string, any>;
}
