import { Module } from '@nestjs/common';
import { AuditlogsService } from './auditlogs.service';
import { AuditlogsController } from './auditlogs.controller';
import { DatabaseModule } from 'src/database/database.module';
import { auditlogProviders } from './auditlog.providers';

@Module({
  imports: [DatabaseModule],
  providers: [...auditlogProviders, AuditlogsService],
  controllers: [AuditlogsController],
  exports: [AuditlogsService],
})
export class AuditlogsModule {}
