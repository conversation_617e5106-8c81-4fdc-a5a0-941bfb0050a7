import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { CreateAuditlogDto } from './dto/create-auditlog.dto';
import { repositories } from 'src/common/constants';
import { Repository } from 'typeorm';
import { Auditlog, AuditlogEntityType } from './entities/auditlog.entity';
import { UsersService } from 'src/users/users.service';
import { ModuleRef } from '@nestjs/core';
import { detailedDiff } from 'deep-object-diff';
import {
  FilterOperator,
  paginate,
  Paginated,
  PaginateQuery,
} from 'nestjs-paginate';

@Injectable()
export class AuditlogsService implements OnModuleInit {
  private userService: UsersService;

  constructor(
    private moduleRef: ModuleRef,
    @Inject(repositories.auditlog)
    private auditlogRepository: Repository<Auditlog>,
  ) {}

  onModuleInit() {
    this.userService = this.moduleRef.get(UsersService, { strict: false });
  }

  async create(createAuditlogDto: CreateAuditlogDto) {
    const user = await this.userService.findOneById(createAuditlogDto.userId);

    const deepObjectDiff = detailedDiff(
      createAuditlogDto.before,
      createAuditlogDto.after,
    );

    const auditlog = this.auditlogRepository.create({
      ...createAuditlogDto,
      user,
      diff: deepObjectDiff,
    });

    return this.auditlogRepository.save(auditlog);
  }

  findAll(query: PaginateQuery): Promise<Paginated<Auditlog>> {
    return paginate(query, this.auditlogRepository, {
      sortableColumns: [
        'id',
        'action',
        'entityType',
        'entityId',
        'user',
        'created',
      ],
      nullSort: 'last',
      defaultSortBy: [['created', 'DESC']],
      searchableColumns: ['action', 'entityType', 'entityId', 'user'],
      select: [
        'id',
        'action',
        'entityType',
        'entityId',
        'user.(firstName)',
        'user.(lastName)',
        'before',
        'diff',
        'created',
      ],
      filterableColumns: {
        entityType: [FilterOperator.EQ, FilterOperator.IN],
      },
      relations: ['user'],
    });

    // return this.auditlogRepository.find({
    //   relations: ['user'],
    //   order: { created: 'DESC' },
    // });
  }

  findAllForEntity(entityType: AuditlogEntityType, entityId: string) {
    return this.auditlogRepository.find({
      relations: ['user'],
      where: {
        entityType,
        entityId,
      },
      order: { created: 'DESC' },
    });
  }

  findOne(id: string) {
    return this.auditlogRepository.findOneBy({ id });
  }
}
