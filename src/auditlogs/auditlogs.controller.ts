import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AuditlogsService } from './auditlogs.service';
import { HasRoles } from 'src/auth/decorators/roles.decorator';
import { UserRole } from 'src/users/entities/user.entity';
import { JwtAccessAuthGuard } from 'src/auth/guards/jwt-access-auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { Auditlog, AuditlogEntityType } from './entities/auditlog.entity';
import { Paginate, Paginated, PaginateQuery } from 'nestjs-paginate';

@Controller('auditlogs')
export class AuditlogsController {
  constructor(private readonly auditlogsService: AuditlogsService) {}

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':entityType/:entityId')
  findAllForEntity(
    @Param('entityType') entityType: AuditlogEntityType,
    @Param('entityId') entityId: string,
  ) {
    return this.auditlogsService.findAllForEntity(entityType, entityId);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get()
  findAll(@Paginate() query: PaginateQuery): Promise<Paginated<Auditlog>> {
    return this.auditlogsService.findAll(query);
  }

  @HasRoles(UserRole.ADMIN, UserRole.MANAGER)
  @UseGuards(JwtAccessAuthGuard, RolesGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.auditlogsService.findOne(id);
  }
}
