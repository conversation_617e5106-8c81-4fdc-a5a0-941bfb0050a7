import { Injectable } from '@nestjs/common';

@Injectable()
export class DashboardsService {
  getOverview() {
    return {
      jobs: {
        total: 0,
        aboveEstimate: 0,
        belowEstimate: 0,
        today: 0,
        todayAboveEstimate: 0,
        todayBelowEstimate: 0,
      },
      assets: {
        collectable: 0,
      },
      places: {
        total: 140,
        dirty: 12,
        cleaning: 48,
        ready: 80,
      },
      inventory: {},
      bags: {},
      incidents: {
        total: 4,
        today: 1,
      },
      users: {
        total: 0,
      },
      vehicles: {},

      // Stats to include:
      // - total staff
      // - total jobs done today
      // - total issues to resolve
      // - total issues solved/unsolved by month
      // - top 5 staff by jobs done
      // - top places by jobs done
    };
  }
}
