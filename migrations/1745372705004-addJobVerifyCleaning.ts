import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobVerifyCleaning1745372705004 implements MigrationInterface {
    name = 'AddJobVerifyCleaning1745372705004'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "job" ADD "verifyCleaning" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "verifyCleaning"`);
    }

}
