import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCleaninigVerifications1745522443037 implements MigrationInterface {
    name = 'AddCleaninigVerifications1745522443037'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "job" ADD "cleaningVerified" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "job" ADD "cleaningVerifiedById" uuid`);
        await queryRunner.query(`ALTER TABLE "job" ADD CONSTRAINT "FK_b9fd63561f2636f2e8ff6d172c4" FOREIGN KEY ("cleaningVerifiedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "job" DROP CONSTRAINT "FK_b9fd63561f2636f2e8ff6d172c4"`);
        await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "cleaningVerifiedById"`);
        await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "cleaningVerified"`);
    }

}
