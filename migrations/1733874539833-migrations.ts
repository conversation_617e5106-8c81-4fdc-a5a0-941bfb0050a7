import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1733874539833 implements MigrationInterface {
  name = 'Migrations1733874539833';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."incident_status_enum" AS ENUM('reported', 'in_progress', 'resolved', 'closed')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."incident_severity_enum" AS ENUM('low', 'medium', 'high')`,
    );
    await queryRunner.query(
      `CREATE TABLE "incident" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "description" character varying NOT NULL, "status" "public"."incident_status_enum" NOT NULL DEFAULT 'reported', "severity" "public"."incident_severity_enum" NOT NULL DEFAULT 'low', "image" character varying, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "placeId" uuid, "assetId" uuid, CONSTRAINT "PK_5f90b28b0b8238d89ee8edcf96e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."timelog_action_enum" AS ENUM('start', 'pause', 'resume', 'stop')`,
    );
    await queryRunner.query(
      `CREATE TABLE "timelog" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "action" "public"."timelog_action_enum" NOT NULL DEFAULT 'start', "location" point, "geofenced" boolean, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "userId" uuid, "jobId" uuid, CONSTRAINT "PK_e5f118a7fedfaa3e78bb2942ffb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "vehicle" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "plate" character varying, "seats" integer NOT NULL, "colour" character varying, "active" boolean NOT NULL DEFAULT true, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, CONSTRAINT "PK_187fa17ba39d367e5604b3d1ec9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "booking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "start" TIMESTAMP NOT NULL, "end" TIMESTAMP NOT NULL, "notes" character varying, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "vehicleId" uuid, "driverId" uuid, CONSTRAINT "PK_49171efc69702ed84c812f33540" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum" AS ENUM('created', 'scheduled', 'started', 'paused', 'resumed', 'canceled', 'delayed', 'inspected', 'done')`,
    );
    await queryRunner.query(
      `CREATE TABLE "job" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying, "notes" character varying, "status" "public"."job_status_enum" NOT NULL DEFAULT 'created', "start" TIMESTAMP, "end" TIMESTAMP, "loggedHours" numeric(5,2), "color" character varying, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "extraSheets" boolean NOT NULL DEFAULT false, "userId" uuid, "placeId" uuid, "bookingId" uuid, CONSTRAINT "PK_98ab1c14ff8d1cf80d18703b92f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "vacation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying, "start" TIMESTAMP NOT NULL, "end" TIMESTAMP NOT NULL, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "userId" uuid, CONSTRAINT "PK_b98b2da5d138aa464c5d1431135" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."auditlog_action_enum" AS ENUM('create', 'update', 'delete', 'import')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."auditlog_entitytype_enum" AS ENUM('user', 'job', 'vacation', 'asset', 'asset_request', 'area', 'place', 'typology', 'bag', 'vehicle', 'incident', 'tag', 'booking')`,
    );
    await queryRunner.query(
      `CREATE TABLE "auditlog" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "action" "public"."auditlog_action_enum" NOT NULL DEFAULT 'create', "entityType" "public"."auditlog_entitytype_enum" NOT NULL DEFAULT 'job', "entityId" character varying, "diff" jsonb, "before" jsonb, "after" jsonb, "created" TIMESTAMP NOT NULL DEFAULT now(), "userId" uuid, CONSTRAINT "PK_fe56b884f5ed4240ee46cee2c98" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_role_enum" AS ENUM('admin', 'manager', 'driver', 'user')`,
    );
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "username" character varying NOT NULL, "password" character varying NOT NULL, "email" character varying NOT NULL, "phone" character varying, "firstName" character varying, "lastName" character varying, "displayName" character varying, "photo" character varying, "role" "public"."user_role_enum" NOT NULL DEFAULT 'user', "refreshToken" character varying, "isDriver" boolean NOT NULL DEFAULT false, "isActive" boolean NOT NULL DEFAULT true, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_request_status_enum" AS ENUM('pending', 'rejected', 'fulfilled')`,
    );
    await queryRunner.query(
      `CREATE TABLE "asset_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "start" TIMESTAMP NOT NULL, "end" TIMESTAMP NOT NULL, "notes" character varying, "status" "public"."asset_request_status_enum" NOT NULL DEFAULT 'pending', "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "assetId" uuid, "placeId" uuid, "userId" uuid, CONSTRAINT "PK_e593fcb27fea5e91a7a9b101d34" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."asset_status_enum" AS ENUM('in_stock', 'in_use', 'collect_pending', 'collect_delayed', 'moving', 'maintenance', 'lost', 'broken', 'disposed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "asset" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "notes" character varying, "image" character varying, "status" "public"."asset_status_enum" NOT NULL DEFAULT 'in_stock', "active" boolean NOT NULL DEFAULT true, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "tagId" uuid, "placeId" uuid, "incidentsId" uuid, "requestsId" uuid, CONSTRAINT "REL_aa9af5056f58d6e4102818e6f6" UNIQUE ("tagId"), CONSTRAINT "PK_1209d107fe21482beaea51b745e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "tag" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "serial" character varying NOT NULL, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, CONSTRAINT "PK_8e4052373c579afc1471f526760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_33b45f5f6c9bbb803f359f9b58" ON "tag" ("serial") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."bag_status_enum" AS ENUM('place_empty', 'place_clean', 'place_dirty', 'place_to_laundry', 'laundry_empty', 'laundry_clean', 'laundry_dirty', 'laundry_to_place')`,
    );
    await queryRunner.query(
      `CREATE TABLE "bag" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "notes" character varying, "status" "public"."bag_status_enum" NOT NULL DEFAULT 'place_empty', "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "tagId" uuid, "placeId" uuid, CONSTRAINT "REL_60bc7d19cbc20d998697a2c599" UNIQUE ("tagId"), CONSTRAINT "PK_6e681d0246f71dc274b5a5d9955" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "typology" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, CONSTRAINT "PK_07ad25b46b31572df068c2886ea" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."place_status_enum" AS ENUM('ready', 'cleaning', 'dirty')`,
    );
    await queryRunner.query(
      `CREATE TABLE "place" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "description" character varying, "status" "public"."place_status_enum" NOT NULL DEFAULT 'ready', "image" character varying, "address" character varying, "location" point, "cleaningEstimate" integer, "notes" character varying, "isActive" boolean NOT NULL DEFAULT true, "tasks" jsonb, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "typologyId" uuid, "tagId" uuid, "areaId" uuid, CONSTRAINT "REL_166089d6a74ab8d6512a5c56d4" UNIQUE ("tagId"), CONSTRAINT "PK_96ab91d43aa89c5de1b59ee7cca" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "area" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "colour" character varying, "created" TIMESTAMP NOT NULL DEFAULT now(), "updated" TIMESTAMP NOT NULL DEFAULT now(), "deleted" TIMESTAMP, "parentId" uuid, CONSTRAINT "PK_39d5e4de490139d6535d75f42ff" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "vehicle_areas_area" ("vehicleId" uuid NOT NULL, "areaId" uuid NOT NULL, CONSTRAINT "PK_7552f048d6746625cd623b3aba2" PRIMARY KEY ("vehicleId", "areaId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e3f2182a8b41ced70d3a3a7117" ON "vehicle_areas_area" ("vehicleId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_218dc338c5344c4cfe2608641b" ON "vehicle_areas_area" ("areaId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "booking_passengers_user" ("bookingId" uuid NOT NULL, "userId" uuid NOT NULL, CONSTRAINT "PK_934b1d7b2a60bd20a3b4849f746" PRIMARY KEY ("bookingId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ac137d0b29aa85fb2ada70b34e" ON "booking_passengers_user" ("bookingId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ce0ac7f242de1521278d19556e" ON "booking_passengers_user" ("userId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "job_helpers_user" ("jobId" uuid NOT NULL, "userId" uuid NOT NULL, CONSTRAINT "PK_c6937f97ef3b221e76b56b0b1c0" PRIMARY KEY ("jobId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9c44a2981b736755c6d0449dff" ON "job_helpers_user" ("jobId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3d758264dbf4ee4aae04ea7415" ON "job_helpers_user" ("userId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "incident" ADD CONSTRAINT "FK_182795c2c41541a09e0f96eb1f8" FOREIGN KEY ("placeId") REFERENCES "place"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident" ADD CONSTRAINT "FK_b7f30869a9120693751a8ea6153" FOREIGN KEY ("assetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "timelog" ADD CONSTRAINT "FK_95abcbb18c2e03a7037bd40e89c" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "timelog" ADD CONSTRAINT "FK_dc5e009600966fca484cfb48c90" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking" ADD CONSTRAINT "FK_dc9f6a94644e45d49872c1e2f10" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking" ADD CONSTRAINT "FK_bc7709bc4dec5c96d0f660d2873" FOREIGN KEY ("driverId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD CONSTRAINT "FK_308fb0752c2ea332cb79f52ceaa" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD CONSTRAINT "FK_61871654e17dedfb55193f6b61f" FOREIGN KEY ("placeId") REFERENCES "place"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD CONSTRAINT "FK_e13342fe16d8fcad7070d85a5b5" FOREIGN KEY ("bookingId") REFERENCES "booking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vacation" ADD CONSTRAINT "FK_b8ecb37781cb6faa4503793f6f3" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "auditlog" ADD CONSTRAINT "FK_22293be6e0988104d1b01985060" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset_request" ADD CONSTRAINT "FK_a7f5f0821433d5692b33b7b5fe1" FOREIGN KEY ("assetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset_request" ADD CONSTRAINT "FK_db28d076513d515513e1e407d13" FOREIGN KEY ("placeId") REFERENCES "place"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset_request" ADD CONSTRAINT "FK_6827f22a7bc564f462521281896" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ADD CONSTRAINT "FK_aa9af5056f58d6e4102818e6f63" FOREIGN KEY ("tagId") REFERENCES "tag"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ADD CONSTRAINT "FK_8fc1e10214a9ffc8c7c3d33a6dc" FOREIGN KEY ("placeId") REFERENCES "place"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ADD CONSTRAINT "FK_75eeb67700dec6e1ce7d74f38dd" FOREIGN KEY ("incidentsId") REFERENCES "incident"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" ADD CONSTRAINT "FK_e026adde0cb80bf61687ba15ee7" FOREIGN KEY ("requestsId") REFERENCES "asset_request"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bag" ADD CONSTRAINT "FK_60bc7d19cbc20d998697a2c599b" FOREIGN KEY ("tagId") REFERENCES "tag"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bag" ADD CONSTRAINT "FK_99f46e341da6f580dbff8184fb1" FOREIGN KEY ("placeId") REFERENCES "place"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "place" ADD CONSTRAINT "FK_8bc344fabe947c586d8997465c9" FOREIGN KEY ("typologyId") REFERENCES "typology"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "place" ADD CONSTRAINT "FK_166089d6a74ab8d6512a5c56d48" FOREIGN KEY ("tagId") REFERENCES "tag"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "place" ADD CONSTRAINT "FK_4056d52547dc0fe9c7d76c64ea0" FOREIGN KEY ("areaId") REFERENCES "area"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "area" ADD CONSTRAINT "FK_f2b11f740f68f3ccc2857a1f140" FOREIGN KEY ("parentId") REFERENCES "area"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_areas_area" ADD CONSTRAINT "FK_e3f2182a8b41ced70d3a3a71170" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_areas_area" ADD CONSTRAINT "FK_218dc338c5344c4cfe2608641b8" FOREIGN KEY ("areaId") REFERENCES "area"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking_passengers_user" ADD CONSTRAINT "FK_ac137d0b29aa85fb2ada70b34ed" FOREIGN KEY ("bookingId") REFERENCES "booking"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking_passengers_user" ADD CONSTRAINT "FK_ce0ac7f242de1521278d19556e3" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_helpers_user" ADD CONSTRAINT "FK_9c44a2981b736755c6d0449dffd" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_helpers_user" ADD CONSTRAINT "FK_3d758264dbf4ee4aae04ea74154" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_helpers_user" DROP CONSTRAINT "FK_3d758264dbf4ee4aae04ea74154"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_helpers_user" DROP CONSTRAINT "FK_9c44a2981b736755c6d0449dffd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking_passengers_user" DROP CONSTRAINT "FK_ce0ac7f242de1521278d19556e3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking_passengers_user" DROP CONSTRAINT "FK_ac137d0b29aa85fb2ada70b34ed"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_areas_area" DROP CONSTRAINT "FK_218dc338c5344c4cfe2608641b8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_areas_area" DROP CONSTRAINT "FK_e3f2182a8b41ced70d3a3a71170"`,
    );
    await queryRunner.query(
      `ALTER TABLE "area" DROP CONSTRAINT "FK_f2b11f740f68f3ccc2857a1f140"`,
    );
    await queryRunner.query(
      `ALTER TABLE "place" DROP CONSTRAINT "FK_4056d52547dc0fe9c7d76c64ea0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "place" DROP CONSTRAINT "FK_166089d6a74ab8d6512a5c56d48"`,
    );
    await queryRunner.query(
      `ALTER TABLE "place" DROP CONSTRAINT "FK_8bc344fabe947c586d8997465c9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bag" DROP CONSTRAINT "FK_99f46e341da6f580dbff8184fb1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bag" DROP CONSTRAINT "FK_60bc7d19cbc20d998697a2c599b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" DROP CONSTRAINT "FK_e026adde0cb80bf61687ba15ee7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" DROP CONSTRAINT "FK_75eeb67700dec6e1ce7d74f38dd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" DROP CONSTRAINT "FK_8fc1e10214a9ffc8c7c3d33a6dc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset" DROP CONSTRAINT "FK_aa9af5056f58d6e4102818e6f63"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset_request" DROP CONSTRAINT "FK_6827f22a7bc564f462521281896"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset_request" DROP CONSTRAINT "FK_db28d076513d515513e1e407d13"`,
    );
    await queryRunner.query(
      `ALTER TABLE "asset_request" DROP CONSTRAINT "FK_a7f5f0821433d5692b33b7b5fe1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "auditlog" DROP CONSTRAINT "FK_22293be6e0988104d1b01985060"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vacation" DROP CONSTRAINT "FK_b8ecb37781cb6faa4503793f6f3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" DROP CONSTRAINT "FK_e13342fe16d8fcad7070d85a5b5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" DROP CONSTRAINT "FK_61871654e17dedfb55193f6b61f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" DROP CONSTRAINT "FK_308fb0752c2ea332cb79f52ceaa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking" DROP CONSTRAINT "FK_bc7709bc4dec5c96d0f660d2873"`,
    );
    await queryRunner.query(
      `ALTER TABLE "booking" DROP CONSTRAINT "FK_dc9f6a94644e45d49872c1e2f10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "timelog" DROP CONSTRAINT "FK_dc5e009600966fca484cfb48c90"`,
    );
    await queryRunner.query(
      `ALTER TABLE "timelog" DROP CONSTRAINT "FK_95abcbb18c2e03a7037bd40e89c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident" DROP CONSTRAINT "FK_b7f30869a9120693751a8ea6153"`,
    );
    await queryRunner.query(
      `ALTER TABLE "incident" DROP CONSTRAINT "FK_182795c2c41541a09e0f96eb1f8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3d758264dbf4ee4aae04ea7415"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9c44a2981b736755c6d0449dff"`,
    );
    await queryRunner.query(`DROP TABLE "job_helpers_user"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ce0ac7f242de1521278d19556e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ac137d0b29aa85fb2ada70b34e"`,
    );
    await queryRunner.query(`DROP TABLE "booking_passengers_user"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_218dc338c5344c4cfe2608641b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e3f2182a8b41ced70d3a3a7117"`,
    );
    await queryRunner.query(`DROP TABLE "vehicle_areas_area"`);
    await queryRunner.query(`DROP TABLE "area"`);
    await queryRunner.query(`DROP TABLE "place"`);
    await queryRunner.query(`DROP TYPE "public"."place_status_enum"`);
    await queryRunner.query(`DROP TABLE "typology"`);
    await queryRunner.query(`DROP TABLE "bag"`);
    await queryRunner.query(`DROP TYPE "public"."bag_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_33b45f5f6c9bbb803f359f9b58"`,
    );
    await queryRunner.query(`DROP TABLE "tag"`);
    await queryRunner.query(`DROP TABLE "asset"`);
    await queryRunner.query(`DROP TYPE "public"."asset_status_enum"`);
    await queryRunner.query(`DROP TABLE "asset_request"`);
    await queryRunner.query(`DROP TYPE "public"."asset_request_status_enum"`);
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
    await queryRunner.query(`DROP TABLE "auditlog"`);
    await queryRunner.query(`DROP TYPE "public"."auditlog_entitytype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."auditlog_action_enum"`);
    await queryRunner.query(`DROP TABLE "vacation"`);
    await queryRunner.query(`DROP TABLE "job"`);
    await queryRunner.query(`DROP TYPE "public"."job_status_enum"`);
    await queryRunner.query(`DROP TABLE "booking"`);
    await queryRunner.query(`DROP TABLE "vehicle"`);
    await queryRunner.query(`DROP TABLE "timelog"`);
    await queryRunner.query(`DROP TYPE "public"."timelog_action_enum"`);
    await queryRunner.query(`DROP TABLE "incident"`);
    await queryRunner.query(`DROP TYPE "public"."incident_severity_enum"`);
    await queryRunner.query(`DROP TYPE "public"."incident_status_enum"`);
  }
}
