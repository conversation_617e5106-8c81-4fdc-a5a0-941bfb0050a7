import { MigrationInterface, QueryRunner } from "typeorm";

export class AddJobCleaningType1745344703213 implements MigrationInterface {
    name = 'AddJobCleaningType1745344703213'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "job" ADD "cleaningType" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "cleaningType"`);
    }

}
