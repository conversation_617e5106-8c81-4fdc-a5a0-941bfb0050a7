import { MigrationInterface, QueryRunner } from "typeorm";

export class AddVerifierFlag1745539729412 implements MigrationInterface {
    name = 'AddVerifierFlag1745539729412'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "isVerifier" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isVerifier"`);
    }

}
