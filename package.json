{"name": "dreamlogic-api", "author": "<PERSON><PERSON>", "version": "0.0.1", "description": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build && npm run sentry:sourcemaps", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register node_modules/typeorm/cli", "migration:generate": "npx ts-node -P ./tsconfig.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:generate -d ./src/database/typeorm.ts ./migrations/migration", "typeorm:generate-migration": "npm run typeorm -- -d ./src/database/typeorm.ts migration:generate ./migrations/$npm_config_name", "typeorm:run-migrations": "npm run typeorm -- -d ./src/database/typeorm.ts migration:run", "typeorm:create-migration": "npm run typeorm -- migration:create ./migrations/$npm_config_name", "typeorm:revert-migration": "npm run typeorm -- -d ./src/database/typeorm.ts migration:revert", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org dreamalgarve --project dreamalgarve-api ./dist && sentry-cli sourcemaps upload --org dreamalgarve --project dreamalgarve-api ./dist"}, "dependencies": {"@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/serve-static": "^4.0.2", "@nestjs/typeorm": "^10.0.0", "@sentry/cli": "^2.31.2", "@sentry/node": "^8.5.0", "@sentry/profiling-node": "^8.5.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "cache-manager": "^6.4.1", "class-transformer": "^0.5.1", "date-fns": "^3.6.0", "deep-object-diff": "^1.1.9", "geolib": "^3.3.4", "nestjs-paginate": "^9.0.0", "node-xlsx": "^0.24.0", "openid-client": "^6.3.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "rxjs": "^7.8.1", "sharp": "^0.33.4", "typeorm": "^0.3.17", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/sharp": "^0.32.0", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "reflect-metadata": "^0.1.14", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}